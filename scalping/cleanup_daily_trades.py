
import asyncio
from collections import defaultdict
import logging

import utils
from telegram_utils import TelegramUtil

logging.basicConfig(level=logging.INFO,
                    format="%(asctime)s %(levelname)s: %(message)s",
                    datefmt="%Y-%m-%d %H:%M:%S")

class DailyTradeCleanup:
    def __init__(self):
        self.config = utils.get_config()
        self.telegram_client = TelegramUtil(config=self.config, client=True, bot=False, session_name='daily_trade_cleanup')
        self.telegram_chat_id = -1002075758731

    def task(self):
        logging.info("Starting cleanup task...")
        todays_msgs = self.telegram_client.read_todays_messages(self.telegram_chat_id)
        if not todays_msgs:
            logging.info("No messages found for today. Exiting.")
            return

        trade_replies = defaultdict(list)
        for msg in todays_msgs:
            if msg.is_reply and msg.reply_to and msg.message and ("Up:" in msg.message or "SL hit:" in msg.message):
                trade_replies[msg.reply_to.reply_to_msg_id].append(msg)

        if not trade_replies:
            logging.info("No trade replies found to clean up. Exiting.")
            return

        messages_to_delete_ids = []
        for original_msg_id, replies in trade_replies.items():
            if len(replies) > 1:
                # Sort by date to ensure the last one is truly the last
                replies.sort(key=lambda m: m.date)
                # Keep the last message, delete the rest
                for msg_to_delete in replies[:-1]:
                    messages_to_delete_ids.append(msg_to_delete.id)
                logging.info(f"For original message {original_msg_id}, keeping last of {len(replies)} replies and deleting {len(replies)-1}.")

        if messages_to_delete_ids:
            logging.info(f"Found {len(messages_to_delete_ids)} messages to delete.")
            loop = asyncio.get_event_loop()
            loop.run_until_complete(self.telegram_client.delete_messages(chatid=self.telegram_chat_id, message_ids=messages_to_delete_ids))
            logging.info("Cleanup complete.")
        else:
            logging.info("No redundant messages to delete.")

if __name__ == "__main__":
    cleanup_job = DailyTradeCleanup()
    cleanup_job.task()
