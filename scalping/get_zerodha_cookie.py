import heroku3
import browser_cookie3
import time
heroku_conn = heroku3.from_key('************************************')
app = heroku_conn.apps()[0]
config = app.config()
heroku_config_enctoken = config['ENCTOKEN']

while True:
    cookies = browser_cookie3.chrome(domain_name='kite.zerodha.com')
    browser_enctoken = [c.value for c in cookies if c.name=='enctoken']
    browser_enctoken = browser_enctoken[0] if browser_enctoken else None
    if browser_enctoken and browser_enctoken!=heroku_config_enctoken:
        heroku_config_enctoken = browser_enctoken
        config['ENCTOKEN'] = browser_enctoken
    time.sleep(30)
