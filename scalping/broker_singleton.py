import utils
from broker import Zerodha

class BrokerSingleton:
    _self = None

    def init_broker(self):
        config = utils.get_config()
        self.broker = Zerodha(
            config.zerodha_username,
            api_key=config.zerodha_api_key,
            access_token=config.zerodha_access_token
        )

    def __new__(cls):
        if cls._self is None:
            cls._self = super().__new__(cls)
            cls._self.init_broker()
        return cls._self

    def __getattr__(self, attr):
        return getattr(self.broker, attr)
