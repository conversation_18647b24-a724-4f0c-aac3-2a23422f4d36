import os
import threading
from datetime import datetime, timedelta
import utils
from telegram_utils import TelegramUtil
from broker import Zerodha
import time
import psycopg2
import psycopg2.pool
import logging
from multiprocessing import Process
import urllib.request
from broker_singleton import Broker<PERSON><PERSON><PERSON>
from unusual_option_activity import UnusualOptionActivity
from filtered_instruments import FilteredInstruments
import pandas as pd
logging.basicConfig(level=logging.INFO,
                    format="%(asctime)s %(levelname)s: %(message)s",
                    datefmt="%Y-%m-%d %H:%M:%S")

config = utils.get_config()
broker = BrokerSingleton()

df_raw = pd.read_csv('zerodha_instruments.csv')
nse_stocks = df_raw.loc[(df_raw['exchange']=='NSE') & (df_raw['segment']=='NSE')]
df_stocks = df_raw.loc[(df_raw['exchange']=='NFO') & ~df_raw['name'].isin(['NIFTY', 'BANKNIFTY', 'FINNIFTY', 'MIDCPNIFTY'])]
df_fno_stocks = df_stocks.loc[(df_stocks['exchange']=='NFO') & (df_stocks['segment']=='NFO-FUT') & ~df_stocks['name'].isin(['NIFTY', 'BANKNIFTY', 'FINNIFTY', 'MIDCPNIFTY'])]
fno_stocks = df_fno_stocks['name'].to_list()
nse_stocks = nse_stocks.loc[(nse_stocks['tradingsymbol'].isin(fno_stocks))]
nse_stocks['name'] = nse_stocks['tradingsymbol']

for index, stock in nse_stocks.iterrows():
    
    start_date = datetime.strptime('2024-01-01', '%Y-%m-%d')
    end_date = datetime.now()
    current_date = start_date
    df = pd.DataFrame()

    while current_date < end_date:
        next_date = current_date + timedelta(days=50)
        if next_date > end_date:
            next_date = end_date
        q = broker.kite.historical_data(instrument_token=stock['instrument_token'], from_date=current_date.strftime('%Y-%m-%d 09:15:00'), to_date=next_date.strftime('%Y-%m-%d 15:30:00'), interval='minute')
        df_temp = pd.DataFrame(q)
        # print(df_temp)
        df_temp.insert(0, 'ticker', stock['tradingsymbol'])
        df = pd.concat([df, df_temp], ignore_index=True)
        # print("Done for: ", stock['tradingsymbol'], "from", current_date.strftime('%Y-%m-%d'), "to", next_date.strftime('%Y-%m-%d'))
        current_date = next_date + timedelta(days=1)
    # df.insert(0, 'ticker', stock['tradingsymbol'])
    df.to_csv('2024/fno_stocks_1min_data/'+stock['tradingsymbol']+'.csv', index=False)
    print("Done for: ", stock['tradingsymbol'])

import glob

import pickle

df_all_stocks = pd.concat([pd.read_csv(file) for file in glob.glob('2024/fno_stocks_1min_data/*.csv')])
df_all_stocks.rename(columns={'ticker': 'spot_symbol', 'date': 'time', 'close': 'spot_price'}, inplace=True)
df_all_stocks['time'] = pd.to_datetime(df_all_stocks['time'], format='%Y-%m-%d %H:%M:%S%z')
df_all_stocks = df_all_stocks.sort_values(['time', 'spot_symbol'])

with open('2024/fno_stocks_1min_data/all_stocks.pkl', 'wb') as f:
    pickle.dump(df_all_stocks, f)
df_all_stocks = pd.read_pickle('2024/fno_stocks_1min_data/all_stocks.pkl')



# import os
# import os

# base_dir = '/Users/<USER>/Downloads/2024/'
# for root, dirs, files in os.walk(base_dir):
#     for file in files:
#         if file.endswith('.csv'):
#             file_path = os.path.join(root, file)
#             df = pd.read_csv(file_path)
#             df.to_pickle(file_path.replace('.csv', '.pkl'))
