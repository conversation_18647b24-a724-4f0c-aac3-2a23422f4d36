import os
from broker.interfaces import BrokerI
from ks_api_client import ks_api
import pandas as pd
from datetime import datetime, date
import calendar
import requests

class KotakAccount:
    def __init__(self, userid, password, access_token, consumer_key, consumer_secret):
        self.userid = userid
        self.password = password
        self.access_token = access_token
        self.consumer_key = consumer_key
        self.consumer_secret = consumer_secret

class Kotak(BrokerI):
    def __init__(self, kotak_account: KotakAccount, parent = False):
        host = "https://ctradeapi.kotaksecurities.com/apim"
        # host = "https://sbx.kotaksecurities.com/apim"
        self.client = ks_api.KSTradeApi(access_token = kotak_account.access_token, userid = kotak_account.userid, consumer_key = kotak_account.consumer_key, ip = "127.0.0.1", app_id = "test", \
                        host = host, consumer_secret = kotak_account.consumer_secret)
        self.client.login(password = kotak_account.password)
        self.client.session_2fa()
        if parent:
            master_base_url = "https://preferred.kotaksecurities.com/security/production/"
            filename = [f for f in os.listdir('.') if f.startswith("TradeApiInstruments_FNO")][0]
            master_file_date = filename.split('TradeApiInstruments_FNO_')[1][:-4]
            date_today = date.today().strftime("%d_%m_%Y")
            if not master_file_date == date_today:
                new_filename = 'TradeApiInstruments_FNO_' + date_today + '.txt'
                try:
                    response = requests.get(master_base_url+new_filename)
                    open(new_filename, "wb").write(response.content)
                except:
                    pass
                finally:
                    os.remove(filename)
                    filename = new_filename
            self.df = pd.read_csv(filename, sep='|')

    def name(self):
        return 'kotak'

    def get_nfo_instrument_id(self, instrument_name, strike_price, ce_pe, expiry_date=None, expiry_month=None):
        expiry_year = int(datetime.today().strftime('%y'))
        # print(instrument_name, expiry_date, expiry_month, strike_price, ce_pe)
        # import pdb; pdb.set_trace()
        reqd_df = pd.DataFrame()
        if not (instrument_name and strike_price and ce_pe):
            return None
        if expiry_month and expiry_month < datetime.today().month:
            expiry_year = expiry_year + 1
        if not expiry_date and expiry_month:
            reqd_df = self.df.loc[(self.df['instrumentName'] == instrument_name) & (self.df['strike'] == float(strike_price)) & (self.df['optionType'] == ce_pe) & (self.df['expiry'].str.contains(calendar.month_abbr[expiry_month].upper()))]
            if not reqd_df.empty:
                reqd_df = reqd_df.iloc[-1]
        elif expiry_date and expiry_month:
            reqd_df = self.df.loc[(self.df['instrumentName'] == instrument_name) & (self.df['strike'] == float(strike_price)) & (self.df['optionType'] == ce_pe) & (self.df['expiry'] == str(expiry_date)+calendar.month_abbr[expiry_month].upper()+str(expiry_year))]
            if not reqd_df.empty:
                reqd_df = reqd_df.iloc[0]
        elif not expiry_date and not expiry_month:
            reqd_df = self.df.loc[(self.df['instrumentName'] == instrument_name) & (self.df['strike'] == float(strike_price)) & (self.df['optionType'] == ce_pe)]
            if not reqd_df.empty:
                reqd_df = reqd_df.iloc[0]
        elif expiry_date == 'next_week':
            reqd_df = self.df.loc[(self.df['instrumentName'] == instrument_name) & (self.df['strike'] == float(strike_price)) & (self.df['optionType'] == ce_pe)]
            if len(reqd_df)>1:
                reqd_df = int(reqd_df.iloc[1])

        if not reqd_df.empty:
            return int(reqd_df['instrumentToken'])
        return None

    def get_quote(self, tradingsymbol):
        return float(self.client.quote(instrument_token = tradingsymbol, quote_type='DEPTH')['success']['depth'][0]['sell'][0]['price'])

    def positions(self):
        return "Dummy positions"

    def get_bulk_quote(self, instruments):
        pass

    def ticker(self):
        pass

    def orders(self):
        pass

    def instruments(self, exchange=None):
        pass

    def get_lot_size(self, instrument_token):
        return int(self.df.loc[(self.df['instrumentToken'] == instrument_token)].iloc[0]['lotSize'])

    def place_order(self, tradingsymbol, transaction_type="BUY", quantity=1, price=None, trigger_price=None):
        if not tradingsymbol:
            print("Instrument ID not found")
            return
        if quantity is None:
            quantity = 1
        if price is None:
            price = self.get_quote(tradingsymbol)
        instrument_token = tradingsymbol
        # lot_size = self.get_lot_size(instrument_token)
        self.client.place_order(order_type = "N", instrument_token = instrument_token, transaction_type = "BUY",
        quantity = quantity, price = price, trigger_price = price,
        validity = "GFD", variety = "REGULAR")

    def modify_order(self, order_id, price, trigger_price=None):
        pass

    def cancel_order(self, order_id):
        pass

    def place_sl_target_gtt(self, tradingsymbol, quantity, curr_price, sl_price, target_price, product, transaction_type):
        pass

    def get_gtts(self):
        pass

    def get_margin(self):
        pass

    def get_ltp(self):
        pass
