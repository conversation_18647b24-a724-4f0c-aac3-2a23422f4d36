import abc


class BrokerI(metaclass=abc.ABCMeta):

    @abc.abstractmethod
    def name(self):
        pass

    @abc.abstractmethod
    def get_ltp(self, instruments, exchange=None):
        pass

    @abc.abstractmethod
    def get_quote(self, ticker):
        pass

    @abc.abstractmethod
    def get_bulk_quote(self, instruments):
        pass

    @abc.abstractmethod
    def positions(self):
        pass

    @abc.abstractmethod
    async def ticker(self):
        pass

    @abc.abstractmethod
    def orders(self):
        pass

    @abc.abstractmethod
    def instruments(self, exchange=None):
        pass

    @abc.abstractmethod
    def place_order(self, tradingsymbol, transaction_type, quantity, product, order_type, price=None, trigger_price=None, variety=None, exchange=None):
        pass

    @abc.abstractmethod
    def modify_order(self, order_id, price, trigger_price=None):
        pass

    @abc.abstractmethod
    def cancel_order(self, order_id):
        pass

    @abc.abstractmethod
    def place_sl_target_gtt(self, tradingsymbol, quantity, curr_price, sl_price, target_price, product, transaction_type):
        pass

    @abc.abstractmethod
    def get_gtts(self):
        pass

    @abc.abstractmethod
    def get_margin(self):
        pass
