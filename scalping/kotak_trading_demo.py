from broker.kotak import KotakAccount
from ks_api_client import ks_api
import utils
config = utils.get_config()
from broker import Kotak
import pandas as pd
# Defining the host is optional and defaults to https://sbx.kotaksecurities.com/apim
# See configuration.py for a list of all supported configuration parameters.

# config.kotak_access_token = '985cee6d-4e57-3f38-885b-75b11ca5fc2a'
# config.kotak_consumer_key = 'rI0k2zZlurfenwg58NKSxJngZ00a'
# config.kotak_consumer_secret = 'BAacS5vdeWOYiKT2sF3K3tgf70Aa'
kotak_acc = KotakAccount(config.kotak_userid, config.kotak_pass, config.kotak_access_token, config.kotak_consumer_key, config.kotak_consumer_secret)
kotak = Kotak(kotak_acc, parent=True)

tradingsymbol = int(kotak.get_nfo_instrument_id('NIFTY', 17800, 'PE', None, None))
print(tradingsymbol)
# jwt_token = 'eyJ4NXQiOiJZelEzWVdZek1HWXdZVGMxTm1RNVlUVXpNalF5TjJNNVlUVXpORGhsWkROak56azVZamRrWWpCak1qUXhPVFUzTmpKbE4yWTBPVGxsWkRobE0yVXhNZyIsImtpZCI6ImdhdGV3YXlfY2VydGlmaWNhdGVfYWxpYXMiLCJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.LX4oFEcbT68I86YQwvIQ-oLI3TNMTbZm9-s2buSUFJJd8TPM4tCPU7S9r1bhSvArrJK7GD0kt9IhAgdGt8K3lGUMQ7S6lieMjtyP4mmhI-6wZTNv85965axxBdRUPnmtaOCE0NuTYpZY_K3bZA3FcoEA7SVFQehohhUeFG9prlKIRnVlhZHkjZ2dlRor1apKHM6tCvBKO5McZb309XtGTj8lHuOsbzM8KMTPsX-OHxTAEtGPMU-0DvXSroCRPdn3VTZDdS2_TmhEMJLKAQhJBTNDtzmkc0_4Kqp5ecOF6mC-8MuZXvWmLEA5AZ5dxBHAYNXaWI6lby25f1fVvDW8xg=='

# Initiate login and generate OTT
# client.login(password = config.kotak_pass)

#Complete login and generate session token
# client.session_2fa()
# p = client.positions(position_type = "OPEN")
# print(p)
#You can choose to use a day-to-day access code by adding accesscode parameter : client.session_2fa(access_code = "")

# df = pd.read_csv('TradeApiInstruments_FNO_22_10_2022.txt', sep='|')
# print(df)
# x = df.loc[(df['instrumentName'] == 'NIFTY') & (df['strike'] == float(17300)) & (df['optionType'] == 'PE')]
# print(x)




# Place an order. 
# Order_type can be "N", "MIS", "MTF". "SOR". Set variety as "AMO" for post-market orders. 
# Please check detailed documentation (see bottom of page) for more details on each variable. 
# Instrument tokens can be found at the following urls (NOTE: Please replace DD_MM_YYYY with the latest date for updated instrument tokens, for example 27_05_2021 will give tokens for 27 may):
# Equity: https://preferred.kotaksecurities.com/security/production/TradeApiInstruments_Cash_DD_MM_YYYY.txt
# Derivatives: https://preferred.kotaksecurities.com/security/production/TradeApiInstruments_FNO_DD_MM_YYYY.txt
# tradingsymbol = 720
q = kotak.client.quote(instrument_token=tradingsymbol, quote_type='DEPTH')
print(q)

print(kotak.get_quote(tradingsymbol))

# kotak.client.place_order(order_type = "N", instrument_token = tradingsymbol, transaction_type = "BUY", quantity = 50, price = 64.0, tag = "string", validity = "GFD", variety = "REGULAR")
# kotak.place_order(tradingsymbol=tradingsymbol)
# kotak.place_order(tradingsymbol = 17911, transaction_type = "BUY",quantity = 1, price = 200, trigger_price = 200)

from kiteconnect_extras import KiteExt
import pyotp
import time

# k = KiteExt(root='https://api-mobile.kite.trade')
# OTP = pyotp.TOTP('LN7ISXVCKQELWOKQVAGPLU7OVVQHFHLB').now()

# k.login_with_credentials('DR6733', 'tr0ubleM@ker', OTP)

# for i in range(1,100):
#     print(k.positions()['net'])
#     time.sleep(5)
# print(k.margins(segment='equity'))
