import utils
import pandas as pd
from datetime import datetime
from broker import Zerodha
from algorithm import <PERSON><PERSON>

def get_filtered_zerodha_instruments(broker):
    # print("hello")
    df = pd.read_csv('/Users/<USER>/Downloads/zerodha_instruments.csv')
    df['expiry'] = pd.to_datetime(df['expiry'], infer_datetime_format=True)
    df = df.loc[(df['exchange']=='NFO') & ~df['name'].isin(['NIFTY', 'BANKNIFTY', 'FINNIFTY', 'MIDCPNIFTY'])]
    current_month = datetime.now().month
    future_prices = df.loc[(df['exchange']=='NFO') & (df['expiry'].dt.month == current_month) & (df['segment']=='NFO-FUT') & ~df['name'].isin(['NIFTY', 'BANKNIFTY', 'FINNIFTY', 'MIDCPNIFTY'])]
    future_prices['token'] = future_prices['exchange']+":"+future_prices['tradingsymbol']
    ltps = broker.get_ltp(list(future_prices['tradingsymbol']))
    ltps = {x:ltps[x]['last_price'] for x in ltps.keys()}
    future_prices['ltp'] = future_prices['token'].map(ltps)

    df = df.merge(future_prices[['name', 'ltp']], on='name', how='left')
    df.rename(columns={'ltp': 'spot_price'}, inplace=True)
    df = df.loc[
        (( (df['instrument_type']=='CE') & (df['strike'].between(df['spot_price']*1.05, df['spot_price']*1.11))) | ((df['instrument_type']=='PE') & (df['strike'].between(df['spot_price']*0.89, df['spot_price']*0.95)))) 
        & (df['expiry'].dt.month == current_month)
    ]
    return list(df['instrument_token'])

if __name__ == "__main__":
    config = utils.get_config()
    # print(config)
    broker = Zerodha(
        config.zerodha_username,
        api_key=config.zerodha_api_key,
        access_token=config.zerodha_access_token
    )
    # print(broker.get_quote('RELIANCE'))
    instruments = get_filtered_zerodha_instruments(broker)
    algo = Algo(config, broker)
    algo.run(instruments)