from filtered_instruments import FilteredInstruments
import utils
from broker import Zerodha
import psycopg2
from psycopg2 import extras

config = utils.get_config()

db_conn = psycopg2.connect(
    host=config.db_host,
    database=config.db_name,
    user=config.db_user,
    port=config.db_port
)

broker = Zerodha(
    config.zerodha_username,
    api_key=config.zerodha_api_key,
    access_token=config.zerodha_access_token
)

f = FilteredInstruments(broker).df_fno_stocks
f = f[['name', 'lot_size']]
nse_stocks = FilteredInstruments(broker).nse_stocks
nse_stocks = nse_stocks[['instrument_token', 'name']]

f = f.merge(nse_stocks, on='name')
# import pdb; pdb.set_trace()
data = f[['name','instrument_token', 'lot_size']].values.tolist()
insert_query = f"""
    INSERT INTO instruments
    (symbol, instrument_token, lot_size)
    VALUES (%s, %s, %s)
"""
with db_conn.cursor() as cur:
    # extras.execute_values(cur, insert_query, data)
    cur.execute("truncate instruments")
    cur.executemany(insert_query, data)
    db_conn.commit()
    cur.close()
    db_conn.close()
