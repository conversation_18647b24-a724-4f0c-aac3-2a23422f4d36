#!/usr/bin/env python
# pylint: disable=unused-argument, wrong-import-position
# This program is dedicated to the public domain under the CC0 license.

"""
Don't forget to enable inline mode with @BotFather

First, a few handler functions are defined. Then, those functions are passed to
the Application and registered at their respective places.
Then, the bot is started and runs until we press Ctrl-C on the command line.

Usage:
Basic inline bot example. Applies different text transformations.
Press Ctrl-C on the command line or send a signal to the process to stop the
bot.
"""
import logging
from html import escape
from uuid import uuid4
from csv import DictReader
import difflib
import utils
# import config
config = utils.get_config()
from broker import Zerodha
import prettytable as pt
import psycopg2
import pandas as pd
broker = Zerodha(
    config.zerodha_username,
    api_key=config.zerodha_api_key,
    access_token=config.zerodha_access_token
)

db_conn = psycopg2.connect(
    host=config.db_host,
    database=config.db_name,
    user=config.db_user,
    port=config.db_port
)

from telegram import __version__ as TG_VER

from telegram import InlineQueryResultArticle, InputTextMessageContent, Update, InlineKeyboardButton
from telegram.constants import ParseMode
from telegram.ext import ApplicationBuilder, CommandHandler, ContextTypes, InlineQueryHandler, CallbackQueryHandler, ChosenInlineResultHandler

# Enable logging
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s", level=logging.INFO
)
logger = logging.getLogger(__name__)
config = utils.get_config()

from datetime import datetime

def get_suffix(d):
    return 'th' if 11<=d<=13 else {1:'st',2:'nd',3:'rd'}.get(d%10, 'th')

# is_monthly_expiry
def get_nice_name(item):
    expiry = datetime.strptime(item['expiry'], '%Y-%m-%d')
    # end_of_month = expiry + relativedelta(day=31)
    # expiry_date = expiry.date
    # last_thursday = end_of_month + relativedelta(weekday=TH(-1))
    # last_wednesday = end_of_month + relativedelta(weekday=WE(-1))
    # last_tuesday = end_of_month + relativedelta(weekday=WE(-1))

    expiry_month = expiry.strftime("%b").upper()
    is_expiry =  expiry_month in item["tradingsymbol"]
    day = str(expiry.day) + get_suffix(expiry.day) + " " if not is_expiry else ""

    # print("is_expiry: ", is_expiry)
    return item["name"] + " " + day + expiry_month + " " + item["strike"] + " " + item["instrument_type"]


instruments = []
with open("zerodha_instruments.csv", 'r') as f:
    dict_reader = DictReader(f)
    instruments = list(dict_reader)
    # print(instruments[0])
    instruments = [d for d in instruments if d["segment"]=="NFO-OPT" and d["exchange"]=="NFO"]
    for i in instruments:
        i.update({"nice_name": get_nice_name(i)})
    # print(instruments[0])
    # print(len(instruments))
    trading_symbols = [i["tradingsymbol"] for i in instruments]
    nice_instruments = [i["nice_name"] for i in instruments]
    nice_name_to_instrument_id = {i["nice_name"]: i["tradingsymbol"] for i in instruments}
    instrument_id_to_nice_name = {i["tradingsymbol"]: i["nice_name"] for i in instruments}
    # instruments.reverse()
    # print(instruments)

    # for i in instruments:
    #     print(i)


def search(q):
    filtered_instruments = [n for n in nice_instruments if n.startswith(q[0])]
    return [instr for instr in filtered_instruments if all(c in instr for c in q)][0:10]


# Define a few command handlers. These usually take the two arguments update and
# context.
async def start(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Send a message when the command /start is issued."""
    await update.message.reply_text("Hi!")


async def help_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Send a message when the command /help is issued."""
    help_msg = """
Buy - @raw_trading_bot buy banknifty 40000 ce
Buy - @raw_trading_bot buy infy 1400 ce
Check Positions - @raw_trading_bot pos
Exit - @raw_trading_bot exit infy 1400 ce
    """

    await update.message.reply_text(help_msg)

async def inline_result(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle the inline query. This is run when you type: @botusername <query>"""
    query = update.chosen_inline_result.query.upper()
    answer = update.to_json()
    ctxt = context.__dict__
    print("ctxt: ", ctxt)
    print("answer: ", answer)
    chosen = update.chosen_inline_result.result_id
    print("chosen: ", chosen)
    quote = broker.get_bulk_quote([chosen])[chosen]
    print("query_accepted: ", query)
    if query.startswith("BUY"):
        query = query.split(" ")
        upsert_db_query = f"""
            INSERT INTO inline_bot_positions
            (time, symbol, price, status)
            VALUES (%s, %s, %s, %s)
            ON CONFLICT (symbol) DO UPDATE SET price = (inline_bot_positions.price + EXCLUDED.price) / 2
        """
        print("Buying: ", datetime.now(), chosen, quote, "OPEN")
        with db_conn.cursor() as cur:
            cur.execute(upsert_db_query, (datetime.now(), chosen, quote, "OPEN"))
            db_conn.commit()

    elif query.startswith("EXIT") or query.startswith("BOOK"):
        query = query.split(" ")
        # lot_size = query[1]
        position_exit_query = f"""
            UPDATE inline_bot_positions set status = 'CLOSED', exit_price=%s where symbol = (select symbol from inline_bot_positions where symbol = %s order by time desc limit 1)
        """
        with db_conn.cursor() as cur:
            cur.execute(position_exit_query, (round(quote, 2), chosen))
            db_conn.commit()

async def inline_query_pnl(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    table_1_week = pt.PrettyTable(['Time', 'Symbol', 'Buy', 'High', 'PNL'])
    table_1_month = pt.PrettyTable(['Time', 'Symbol', 'Buy', 'High', 'PNL'])
    query = update.inline_query.query.upper()
    if not (query.startswith("PNL")):
        return
    exited_positions_query_1_week = f"""
        SELECT time, symbol, price, high_price from inline_bot_positions where high_price is not null and time>current_date - interval '7 days' order by time
    """

    exited_positions_query_1_month = f"""
        SELECT time, symbol, price, high_price from inline_bot_positions where high_price is not null and time>current_date - interval '30 days' order by time
    """

    pnl_1_week = 0.0
    pnl_1_month = 0.0
    
    with db_conn.cursor() as cur:
        cur.execute(exited_positions_query_1_week)
        columns = [column[0] for column in cur.description]
        positions_1_week = []
        for row in cur.fetchall():
            positions_1_week.append(dict(zip(columns, row)))
        for p in positions_1_week:
            high_price = p.get('high_price', 0)
            pnl = round(high_price-p['price'], 2)
            pnl_percentage = str(round((pnl/p['price'])*100 , 2))+'%'
            table_1_week.add_row([p['time'].strftime("%Y-%m-%d"), p['symbol'], p['price'], high_price, pnl_percentage])

        df_table_1_week = pd.DataFrame(table_1_week)
        df_table_1_week.plot(kind="bar")
        cur.execute(exited_positions_query_1_month)
        columns = [column[0] for column in cur.description]
        positions_1_month = []
        for row in cur.fetchall():
            positions_1_month.append(dict(zip(columns, row)))
        for p in positions_1_month:
            high_price = p.get('high_price', 0)
            pnl = round(high_price-p['price'], 2)
            pnl_percentage = str(round((pnl/p['price'])*100 , 2))+'%'
            table_1_month.add_row([p['time'].strftime("%Y-%m-%d"), p['symbol'], p['price'], high_price, pnl_percentage])


    results = [
        InlineQueryResultArticle(
            id=str(uuid4()),
            title="PNL 7 days",
            input_message_content=InputTextMessageContent(f'\n\n<b>PNL 1 week:</b>\n<pre>{table_1_week}</pre>', parse_mode=ParseMode.HTML),
        ),
        InlineQueryResultArticle(
            id=str(uuid4()),
            title="PNL 1 month",
            input_message_content=InputTextMessageContent(f'\n\n<b>PNL 1 month:</b>\n<pre>{table_1_month}</pre>', parse_mode=ParseMode.HTML),
        )
    ]
    await update.inline_query.answer(results, cache_time=0)

async def inline_query_positions(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    query = update.inline_query.query.upper()
    if not (query.startswith("POS")):
        return
    table = pt.PrettyTable(['Symbol', 'Buy Price', 'CMP', 'Status'])
    position_query = f"""
        SELECT * from inline_bot_positions where status = 'OPEN'
    """
    with db_conn.cursor() as cur:
        cur.execute(position_query)
        columns = [column[0] for column in cur.description]
        positions = []
        for row in cur.fetchall():
            positions.append(dict(zip(columns, row)))

    if positions:
        quotes = broker.get_bulk_quote([f['symbol'] for f in positions])

    for p in positions:
        if quotes.get(p['symbol']):
            table.add_row([instrument_id_to_nice_name.get(p['symbol'], p['symbol']), p['price'], quotes.get(p['symbol']), p['status']])

    # input_msg = InputTextMessageContent(f'<pre>{table}</pre>', parse_mode=ParseMode.HTML)
    results = [
        InlineQueryResultArticle(
            id=str(uuid4()),
            title="Positions",
            input_message_content=InputTextMessageContent(f'<pre>{table}</pre>', parse_mode=ParseMode.HTML),
        )
    ]
    await update.inline_query.answer(results, cache_time=0)


async def inline_query_exit(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    query = update.inline_query.query.upper()
    query_prefix = query.split(" ")[0:2]
    if len(query_prefix)<2:
        print("return 212:", len(query_prefix))
        return
    command = query_prefix[0]
    if not (command.startswith("EXIT") or command.startswith("BOOK")):
        print("return1")
        return
    position_query = f"""
        SELECT * from inline_bot_positions where status = 'OPEN'
    """
    with db_conn.cursor() as cur:
        cur.execute(position_query)
        columns = [column[0] for column in cur.description]
        positions = []
        for row in cur.fetchall():
            positions.append(dict(zip(columns, row)))

    if positions:
        quotes = broker.get_bulk_quote([f['symbol'] for f in positions])

    exit_positions = []
    for p in positions:
        symbol = p['symbol']
        title = instrument_id_to_nice_name.get(p['symbol'])
        if quotes.get(p['symbol']) and p.get('price'):
            cmp = str(quotes[p['symbol']])
            return_value = str(round(100*(quotes[p['symbol']]-p['price'])/p['price'], 2))+"%"
            exit_positions.append({'symbol': symbol, 'title':title, 'cmp': cmp, 'return_value': return_value})
    results = [
        InlineQueryResultArticle(
            id=p['symbol'],
            title=p['title'],
            input_message_content=InputTextMessageContent("Exit "+p['title']+"\nCMP: "+p['cmp'] + 
                                                          "\nReturn " + p['return_value'])
        ) for p in exit_positions
    ]
    await update.inline_query.answer(results, cache_time=0)


async def inline_query_buy(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    query = update.inline_query.query.upper()
    print("query: ", query)
    query_prefix = query.split(" ")[0:2]

    if len(query_prefix)<2:
        return

    command = query_prefix[0]
    if not (command.startswith("BUY")):
        print("return1")
        return
    instrument_input = query.split(" ")[1:]
    if len(instrument_input)==0:
        print("return2")
        return
    print("instrument_input: ", instrument_input)
    filtered_instruments = search(instrument_input)

    if not filtered_instruments:
        print("return3")
        return
    print("len_filtered: ", len(filtered_instruments))
    # quotes = algo.broker.get_bulk_quote(filtered_instruments)
    quotes = broker.get_bulk_quote(nice_name_to_instrument_id[f] for f in filtered_instruments)
    print(filtered_instruments)
    print(quotes)
    results = [
        InlineQueryResultArticle(
            id=nice_name_to_instrument_id[f],
            title=f,
            input_message_content=InputTextMessageContent("Watch "+f+"\n CMP: "+str(quotes[nice_name_to_instrument_id[f]])),
        ) for f in filtered_instruments
    ]

    await update.inline_query.answer(results, cache_time=30)
    # await update.inline_query.get_bot().answer_inline_query(results)

async def inline_query(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle the inline query. This is run when you type: @botusername <query>"""
    print(context._user_id)
    print(context)
    query = update.inline_query.query
    # quote = algo.broker.get_bulk_quote(['BANKNIFTY22OCT42000CE'])
    # print(quote)
    if query == "":
        print("did not get anything")
        return
    # filtered_instruments = process.extract(query, nice_instruments, limit=10)
    # filtered_instruments = [f[0] for f in filtered_instruments]
    query = query.upper()

    if query.startswith("BUY"):
        return await inline_query_buy(update, context)
    elif query.startswith("HOLD") or query.startswith("EXIT"):
        return await inline_query_exit(update, context)
    elif query.startswith("POS"):
        return await inline_query_positions(update, context)
    elif query.startswith("PNL"):
        return await inline_query_pnl(update, context)


async def button(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Parses the CallbackQuery and updates the message text."""
    query = update.callback_query

    # CallbackQueries need to be answered, even if no notification to the user is needed
    # Some clients may have trouble otherwise. See https://core.telegram.org/bots/api#callbackquery
    await query.answer()

    await query.edit_message_text(text=f"Selected option: {query.data}")

def main() -> None:
    """Run the bot."""
    # Create the Application and pass it your bot's token.
    global instruments
    print("Starting bot")
    # quote = broker.get_bulk_quote(['FINNIFTY23N1319350PE'])
    # print("quote: ", quote)
    # filtered = difflib.get_close_matches('BANKNIFTYDEC', instruments, n=10, cutoff=0.1)
    # print(filtered)
    # filtered = process.extract("banknifty 40000 ce", instruments, limit=10)
    # print(filtered)
    # application = Application.builder().token("**********:AAFbIM2gDEHIYJhNxC03PV9ZIOqnyBecnDw").build()
    application = ApplicationBuilder().token("**********:AAHWlqjBo5vPSH_wMGVy0F9nXzuPdoLjjQM").build()
    # on different commands - answer in Telegram
    application.add_handler(CommandHandler("start", start))
    application.add_handler(CommandHandler("help", help_command))
    application.add_handler(CallbackQueryHandler(button))
    # on non command i.e message - echo the message on Telegram
    application.add_handler(InlineQueryHandler(inline_query))
    application.add_handler(ChosenInlineResultHandler(inline_result))

    # Run the bot until the user presses Ctrl-C
    application.run_polling()

# **********:AAEt9ZDQgc98vfdEun8d_rLkczwAdzk3uss

if __name__ == "__main__":
    print("hello")
    logger.warning("Update ")
    main()

