import utils
from telegram_utils import TelegramUtil
from datetime import datetime, timedelta, time
import psycopg2
import sys
import traceback
import prettytable as pt
import re
import threading
import json
from rabbitmq_channel_singleton import RabbitMQChannelSingleton
import signal
import sys
import asyncio

signal.signal(signal.SIGINT, lambda x, y: sys.exit(0))

def is_outside_business_hours():
    now = datetime.now()
    if now.weekday() in [5, 6]:
        return True  # It's a weekend
    start_time = time(hour=9, minute=15)
    end_time = time(hour=15, minute=30)
    if start_time <= now.time() <= end_time:
        return False
    return True


class QueryBuilder:
    """Handles query construction and data filtering logic"""

    def __init__(self, tf, price_multiplier_range, vol_multiplier, oi_multiplier, traded_value, non_index):
        self.tf = tf
        self.price_multiplier_range = price_multiplier_range
        self.vol_multiplier = vol_multiplier
        self.oi_multiplier = oi_multiplier
        self.traded_value = traded_value
        self.non_index = non_index

    def construct_query(self):
        """Build the main query for unusual option activity detection"""
        now = datetime.now()
        current_time_bucket = now.replace(minute=now.minute-now.minute%self.tf, second=0, microsecond=0)
        prev_time_bucket = current_time_bucket - timedelta(minutes=self.tf)
        prev_time_bucket = prev_time_bucket.strftime("%Y-%m-%d %H:%M:%S")
        market_start_time = now.replace(hour=9, minute=15, second=0, microsecond=0).strftime("%Y-%m-%d %H:%M:%S")

        query = f"""
            SELECT t1.symbol as symbol, t1.time as time_from, t2.time as time_to, t1.price as price_from, t2.price as price_to, t1.volume as vol_from, t2.volume as vol_to, t2.vwap as vwap, t2.ema9 as ema9, t2.ema20 as ema20
            FROM ohlc_{self.tf}min t1
            JOIN ohlc_{self.tf}min t2
            ON t1.symbol = t2.symbol AND t1.time = t2.time - INTERVAL '{self.tf} minute'
            WHERE
            t2.time = '{current_time_bucket}'
            AND t1.price>2
            AND t2.oi!=0
            AND t2.price>=t1.price*{self.price_multiplier_range[0]} AND t2.price<t1.price*{self.price_multiplier_range[1]}
            AND t2.price >= (select max(price) from ohlc_{self.tf}min where symbol = t1.symbol and time<t2.time AND time>='{market_start_time}')
        """

        # Add conditional filters
        if self.traded_value:
            query += f" AND (t2.price-t1.price)*t2.volume >= {self.traded_value}"
        if self.vol_multiplier != 1:
            query += f" AND t2.volume >= (select avg(volume) from ohlc_{self.tf}min where symbol = t1.symbol and time<t2.time AND time>='{market_start_time}') * {self.vol_multiplier}"
        if self.oi_multiplier != 1:
            query += f" AND t2.oi >= t1.oi * {self.oi_multiplier}"
        if self.non_index:
            query += f" AND t1.symbol not like '%NIFTY%'"
        else:
            query += f" AND t1.symbol like '%NIFTY%'"

        query += f"AND t2.price>t2.vwap AND t2.vwap>t2.ema9 AND t2.ema9>t2.ema20 "

        return query

    def get_spot_price_query(self):
        """Get query for fetching spot price"""
        return f"SELECT price from ohlc_{self.tf}min where symbol=%s order by time desc limit 1"

    def filter_results(self, results):
        """Apply business logic filters to results"""
        # Filter based on strike price vs spot price for CE/PE
        filtered_results = [
            res for res in results
            if ((res['strike_price'] >= 1.01 * res['spot_price'] and res['ce_pe'] == 'CE') or
                (res['strike_price'] <= 0.99 * res['spot_price'] and res['ce_pe'] == 'PE'))
        ]
        return filtered_results


class AlertFormatter:
    """Handles alert formatting and messaging logic"""

    def __init__(self, tf, price_multiplier_range, vol_multiplier, oi_multiplier, traded_value):
        self.tf = tf
        self.price_multiplier_range = price_multiplier_range
        self.vol_multiplier = vol_multiplier
        self.oi_multiplier = oi_multiplier
        self.traded_value = traded_value

    def to_pretty_table(self, title, msg_rows):
        """Format data into a pretty table"""
        table = pt.PrettyTable(['Time', 'Symbol', 'Price', 'SpotPrice'])
        table.align['Time'] = 'l'
        table.align['Symbol'] = 'l'
        table.align['Price'] = 'r'
        table.align['SpotPrice'] = 'r'
        table.add_rows(msg_rows)
        msg = "<b>{}</b><pre>{}</pre>".format(title, str(table))
        return msg

    def alert_summary_to_pretty_table(self, title, msg_rows):
        """Format alert summary into a pretty table"""
        table = pt.PrettyTable(['Symbol', 'AlertedAt'])
        table.align['Symbol'] = 'l'
        table.align['AlertedAt'] = 'l'
        table.add_rows(msg_rows)
        msg = "<b>{}</b><pre>{}</pre>".format(title, str(table))
        return msg

    def build_alert_title(self):
        """Build dynamic alert title based on parameters"""
        title = f"{self.tf} mins alert:"
        if self.price_multiplier_range != [1, 2]:
            title += f" price_incr> {int(100*(self.price_multiplier_range[0]-1))}% |"
        if self.traded_value:
            title += f" traded_value > {self.traded_value} |"
        if self.vol_multiplier != 1:
            title += f" vol_incr > {self.vol_multiplier}x |"
        if self.oi_multiplier != 1:
            title += f" oi_incr > {self.oi_multiplier}x |"
        return title

    def format_unusual_activity_alert(self, rows):
        """Format unusual activity alert message"""
        title = self.build_alert_title()
        msg_rows = []
        for row in rows:
            time_to = row['time_to'].strftime("%Y-%m-%d %H:%M:%S")
            msg_row = [time_to, row['symbol'], row['price_to'], row['spot_price']]
            msg_rows.append(msg_row)
        msg_rows.sort(key=lambda x: x[1])  # order by symbol
        return self.to_pretty_table(title, msg_rows)

    def format_summary_alert(self, rows):
        """Format summary alert message"""
        title = "REPEAT ALERT"
        msg_rows = []
        for row in rows:
            msg_row = [row['spot_symbol'], row['alerted_at'].strftime("%Y-%m-%d %H:%M:%S")]
            msg_rows.append(msg_row)
        return self.alert_summary_to_pretty_table(title, msg_rows)


class MessagingService:
    """Handles all messaging operations"""

    def __init__(self, config):
        self.telegram_bot = TelegramUtil(config=config, client=False, bot=True)
        self.telegram_chat_id = -1001862665110

    async def send_telegram_message(self, msg):
        """Send message via Telegram"""
        print(f"{datetime.now()}: sending alert to telegram from thread_id: {threading.get_ident()}")
        await self.telegram_bot.send_message(chatid=self.telegram_chat_id, msg=msg)

    def send_to_rabbitmq(self, rows):
        """Send data to RabbitMQ"""
        q = RabbitMQChannelSingleton()
        print(f"{datetime.now()} sending_trades: {[r['symbol'] for r in rows]}")
        q.mq_channel.basic_publish(exchange='', routing_key='quotes', body=json.dumps(rows, default=str))


class DatabaseService:
    """Handles all database operations"""

    def __init__(self, db_conn, tf, vol_multiplier, oi_multiplier, traded_value):
        self.db_conn = db_conn
        self.tf = tf
        self.vol_multiplier = vol_multiplier
        self.oi_multiplier = oi_multiplier
        self.traded_value = traded_value

    def execute_query(self, query):
        """Execute query and return results as dictionaries"""
        with self.db_conn.cursor() as cur:
            cur.execute(query)
            columns = [column[0] for column in cur.description]
            results = []
            for row in cur.fetchall():
                results.append(dict(zip(columns, row)))
            return results

    def get_spot_price(self, spot_symbol, spot_price_query):
        """Get spot price for a symbol"""
        with self.db_conn.cursor() as cur:
            cur.execute(spot_price_query, (spot_symbol,))
            spot_price = cur.fetchone()
            return spot_price[0] if spot_price else None

    def save_alert(self, rows):
        """Save alert data to database"""
        save_alert_query = """
            INSERT INTO unusual_option_activity (tf_min, spot_symbol, strike_symbol, traded_value, vol_multiplier, oi_multiplier, price, spot_price, alerted_at) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        with self.db_conn.cursor() as cur:
            data = [
                (self.tf, row['spot_symbol'], row['symbol'], self.traded_value,
                 self.vol_multiplier if self.vol_multiplier != 1 else None,
                 self.oi_multiplier if self.oi_multiplier != 1 else None,
                 row['price_to'], row['spot_price'], row['time_to'])
                for row in rows
            ]
            cur.executemany(save_alert_query, data)
            self.db_conn.commit()


class UnusualOptionActivity:
    """Main orchestrator class for unusual option activity detection"""

    def __init__(self, tf=5, price_multiplier_range=[1,2], vol_multiplier=1, oi_multiplier=1, traded_value=0, non_index=False, send_to_mq=False) -> None:
        config = utils.get_config()
        self.db_conn = psycopg2.connect(
            host=config.db_host,
            database=config.db_name,
            user=config.db_user,
            port=config.db_port
        )
        self.send_to_mq = send_to_mq

        # Initialize separated components
        self.query_builder = QueryBuilder(tf, price_multiplier_range, vol_multiplier, oi_multiplier, traded_value, non_index)
        self.alert_formatter = AlertFormatter(tf, price_multiplier_range, vol_multiplier, oi_multiplier, traded_value)
        self.messaging_service = MessagingService(config)
        self.database_service = DatabaseService(self.db_conn, tf, vol_multiplier, oi_multiplier, traded_value)

    def send_summary_alert(self, rows):
        """Send summary alert for repeated alerts"""
        msg = self.alert_formatter.format_summary_alert(rows)
        try:
            loop = asyncio.get_event_loop()
            loop.run_until_complete(self.messaging_service.send_telegram_message(msg))
        except Exception:
            traceback.print_exc()

    def send_unusual_activity_alert(self, rows):
        """Send unusual activity alert"""
        msg = self.alert_formatter.format_unusual_activity_alert(rows)
        try:
            loop = asyncio.get_event_loop()
            loop.run_until_complete(self.messaging_service.send_telegram_message(msg))
        except Exception:
            traceback.print_exc()

    def save_and_send_alert(self, rows):
        """Save alert to database and send notifications"""
        self.database_service.save_alert(rows)
        if self.send_to_mq:
            self.messaging_service.send_to_rabbitmq(rows)
        self.send_unusual_activity_alert(rows)

    def enrich_results_with_spot_data(self, results):
        """Enrich results with spot symbol, price, and strike data"""
        spot_price_query = self.query_builder.get_spot_price_query()

        for res in results:
            symbol = res['symbol']
            spot_symbol = re.split(r'(\d+)', symbol)[0]
            strike_price = int(re.search(r'(\d+)(?=[CE|PE])', symbol).group(1))
            spot_price = self.database_service.get_spot_price(spot_symbol, spot_price_query)

            res['spot_symbol'] = spot_symbol
            res['spot_price'] = spot_price
            res['strike_price'] = strike_price
            res['ce_pe'] = symbol[-2:]

        return results

    def take_action(self):
        """Main method to detect and process unusual option activity"""
        # Get query and execute it
        query = self.query_builder.construct_query()
        results = self.database_service.execute_query(query)

        if not results:
            return

        # Enrich results with spot data
        results = self.enrich_results_with_spot_data(results)

        # Apply business logic filters
        filtered_results = self.query_builder.filter_results(results)

        if filtered_results:
            self.save_and_send_alert(filtered_results)

# if __name__ == "__main__":
#     if is_outside_business_hours():
#         sys.exit()
#     if len(sys.argv) < 2:
#         raise ValueError("An argument must be provided.")
#     tf = int(sys.argv[1])
#     price_multiplier = 0
#     vol_multiplier = 0

#     if tf == 1:
#         price_multiplier = 1.1
#         vol_multiplier = 2
#     elif tf == 5:
#         price_multiplier = 1.1
#         vol_multiplier = 10
#     else:
#         print("oops")
#         sys.exit()
#     uva = UnusualVolAlert()
#     uva.check_and_send_alert(tf, price_multiplier=price_multiplier, vol_multiplier=vol_multiplier)

