import pandas as pd
import time
from flask import Flask, request, render_template, session, copy_current_request_context, jsonify
from flask_socketio import Socket<PERSON>, emit, disconnect
from threading import Lock
from threading import Thread

import random


async_mode = None
app = Flask(__name__)
app.config['SECRET_KEY'] = 'secret!'
socket_ = SocketIO(app, async_mode=async_mode)
thread = None
thread_lock = Lock()




def background_stuff():
    print('starting backgound task')
    df = pd.read_csv('/Users/<USER>/Downloads/GFDLCM_INDICES_TICK_22022022/NIFTY BANK.NSE_IDX.csv')
    # print(df.to_string())
    df.drop(df.columns[[0, 1, 4, 5, 6, 7, 8, 9]], axis=1, inplace=True)
    df.set_index('Time', inplace=True)
    df['diff'] = df.diff().round(decimals=2)
    df = df.iloc[:]
    for idx, row in df.iterrows():
        if abs(row['diff'])>4:
            # print(idx, "  ", row['LTP'], "        ", row['diff'])
            print(idx, row['LTP'], "diff: ", str(row['diff']))
            # with app.test_request_context('/'):
            #     socket_.emit('my_response',
            #                  {'data': '', 'idx': str(idx), 'diff': str(row['diff']), 'ltp': str(row['LTP']), 'count': 2}, namespace='/test')
        # time.sleep(1)

    while True:
        # print(f'background thread {thread_id}')
        with app.test_request_context('/'):
            socket_.emit('my_response',
                 {'data': 'abc', 'count': 2}, namespace='/test')
        time.sleep(1)


@app.route('/order', methods=['GET', 'POST'])
def buy():
    # Thread(target=background_stuff).start()
    # print(request.form)
    order_time = request.form['order_time']
    order_price = request.form['order_price']
    action = ''
    if 'buy_button' in request.form:
        action = 'buy'
    if 'sell_button' in request.form:
        action = 'sell'

    print(order_time, order_price, action)
    f = open("orders.txt", "a")
    f.write(order_time + ' ' + action + ' ' + order_price + '\n')
    f.close()
    return jsonify({'succss': True})

@app.route('/')
def index():
    # Thread(target=background_stuff).start()
    return render_template('index.html', async_mode=socket_.async_mode)


@socket_.on('my_event', namespace='/test')
def test_message(message):
    session['receive_count'] = session.get('receive_count', 0) + 1
    emit('my_response',
         {'data': message['data'], 'count': session['receive_count']})


@socket_.on('my_buy_event', namespace='/test')
def test_buy_message(message):
    session['receive_count'] = session.get('receive_count', 0) + 1
    print(message['data'])
    emit('bought',
         {'data': 'bought at ' + message['data'], 'count': session['receive_count']})


@socket_.on('my_broadcast_event', namespace='/test')
def test_broadcast_message(message):
    session['receive_count'] = session.get('receive_count', 0) + 1
    emit('my_response',
         {'data': message['data'], 'count': session['receive_count']},
         broadcast=True)


@socket_.on('disconnect_request', namespace='/test')
def disconnect_request():
    @copy_current_request_context
    def can_disconnect():
        disconnect()

    session['receive_count'] = session.get('receive_count', 0) + 1
    emit('my_response',
         {'data': 'Disconnected!', 'count': session['receive_count']},
         callback=can_disconnect)


# @socket_.on('connect', namespace='/test')
# def setupconnect():
#     socket_.start_background_task(background_stuff)


if __name__ == '__main__':
    background_stuff()
    #socket_.start_background_task(background_stuff)
    #socket_.run(app, debug=True)



