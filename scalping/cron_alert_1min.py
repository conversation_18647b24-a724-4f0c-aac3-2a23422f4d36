from croniter import croniter
from datetime import datetime
import time
from unusual_option_activity import UnusualOptionActivity
# The task to be run

uoa = UnusualOptionActivity(tf=1, price_multiplier_range=[1.1, 10], non_index=True, send_to_mq=True)

def task():
    # uva = UnusualVolAlert(tf=5, price_multiplier=1.1, vol_multiplier=10)
    # uva.check_and_send_alert()

    # uva = UnusualVolAlert(tf=1, price_multiplier=1.1, vol_multiplier=2)
    # uva.check_and_send_alert()

    # 3 min oi alert

    uoa.take_action()

    # 5 min vol alert
    # uva = UnusualVolAlert(tf=5, price_multiplier=1.1, traded_value=500000, vol_multiplier=3, non_index=False)
    # uva.check_and_send_alert()

schedule = "* * * * *"
cron = croniter(schedule, datetime.now())
print(__file__)
while True:
    next_execution = cron.get_next(datetime)
    time.sleep((next_execution - datetime.now()).total_seconds()+10)
    task()
