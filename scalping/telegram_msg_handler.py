import pytesseract
from PIL import Image
import os
from trade_observer import TradeObserver
import utils
import re
# from professor_telegram_interpreter import professor_event_handler
import json
from broker_singleton import BrokerSingleton
broker = BrokerSingleton()
from rabbitmq_channel_singleton import RabbitMQChannelSingleton
import datetime

class TelegramMsgHandler:
    def __init__(self, trade_observer: TradeObserver):
        self.trade_observer = trade_observer
        self.chat_id = -1001862665110

    async def telegram_handler_bnsnipers(self, event, telegram_bot):
        # print('{}'.format(event))
        msgs = event.message.message.lower()
        is_buy = False
        is_sl = False
        is_target = False
        qty = 0
        msgs = msgs.replace('@', '')
        msgs = msgs.replace('  ', ' ')
        if msgs.startswith('buy'):
            for msg in msgs.split('. '):
                if msg.startswith('buy'):
                    tokens = msg.split(' ')
                    if len(tokens) <= 3:
                        continue
                    strike_price = tokens[1]
                    symbol = self.next_expiry_prefix(utils.get_nifty_banknifty_prefix(int(strike_price))) + strike_price + tokens[
                        2].upper()
                    for t in tokens[3:]:
                        if t.isdigit():
                            price = int(t)
                            is_buy = True
                        elif t == 'cmp':
                            price = 0
                            is_buy = True
                elif msg.startswith('sl'):
                    tokens = msg.split(' ')
                    if len(tokens) < 2:
                        continue
                    if tokens[1].isdigit():
                        sl_price = int(tokens[1])
                        is_sl = True
            # if is_buy:
            #     print("Buy order " + str(symbol) + "@" + str(price))
            # if is_sl:
            #     print("SL order " + str(symbol) + "@" + str(sl_price))

        else:
            for msg in msgs.split('. '):
                tokens = msg.split(' ')
                if len(tokens) <= 3:
                    continue
                if tokens[0].isdigit() and tokens[1] in ['ce', 'pe']:
                    if tokens[2] == 'sl' and tokens[3].isdigit():
                        strike_price = tokens[0]
                        symbol = self.next_expiry_prefix(utils.get_nifty_banknifty_prefix(int(strike_price))) + strike_price + \
                                 tokens[1].upper()
                        sl_price = int(tokens[3])
                        is_sl = True
                        # print("SL order " + str(symbol) + "@" + str(sl_price))
                    elif tokens[2] == 'target' and tokens[3].isdigit():
                        strike_price = tokens[0]
                        symbol = self.next_expiry_prefix(utils.get_nifty_banknifty_prefix(int(strike_price))) + strike_price + \
                                 tokens[1].upper()
                        target_price = int(tokens[3])
                        is_target = True
                        # print("TARGET order " + str(symbol) + "@" + str(target_price))
        if is_buy or is_sl or is_target:
            qty = self.get_order_qty(symbol)
            orders = self.broker.orders()
            orders = [o for o in orders if o['tradingsymbol'] == symbol]

        if is_buy:
            existing_open_orders = [o for o in orders if o['status'] == 'OPEN']
            existing_open_order = existing_open_orders[0] if existing_open_orders else None
            print("Buy order " + str(symbol) + "@" + str(price))
            if existing_open_order:
                self.broker.modify_order(existing_open_order['order_id'], price=price)
            else:
                if price == 0:
                    self.broker.place_order(tradingsymbol=symbol, transaction_type=Literal.BUY, quantity=qty,
                                            product='MIS', order_type='MARKET')
                else:
                    self.broker.place_order(tradingsymbol=symbol, transaction_type=Literal.BUY, quantity=qty,
                                            product='MIS', order_type='LIMIT', price=price)
                if not self.telegram_buy_orders:
                    self.telegram_buy_orders = [symbol]
                else:
                    self.telegram_buy_orders.append(symbol)
        positions = self.broker.positions()['net']
        # print(positions)

        positions = [p for p in positions if p['tradingsymbol'] == symbol and p['quantity'] > 0]
        existing_position = positions[0] if positions else None
        print("================")
        print(positions)
        print("================")

        if is_sl:
            print("SL order " + str(symbol) + "@" + str(sl_price))
            existing_open_orders = [o for o in orders if o['status'] == 'OPEN']
            existing_open_order = existing_open_orders[0] if existing_open_orders else None
            existing_sl_orders = [o for o in orders if o['status'] == 'TRIGGER PENDING']
            existing_sl_order = existing_sl_orders[0] if existing_sl_orders else None

        #     if existing_sl_order:
        #         self.broker.modify_order(existing_sl_order['order_id'], price=sl_price, trigger_price=sl_price)
        #     elif existing_position or existing_open_order:
        #         self.broker.place_order(tradingsymbol=symbol, transaction_type=Literal.SELL, quantity=qty,
        #                                 product='MIS', order_type='SL', price=sl_price, trigger_price=sl_price)

        # if is_target:
        #     existing_open_target_orders = [o for o in orders if o['status'] == 'OPEN' and o['order_type'] == 'LIMIT']
        #     existing_open_target_order = existing_open_target_orders[0] if existing_open_target_orders else None
        #     print("TARGET order " + str(symbol) + "@" + str(target_price))
        #     if existing_open_target_order:
        #         self.broker.modify_order(existing_open_target_order['order_id'], price=target_price)
        #     elif existing_position:
        #         self.broker.place_order(tradingsymbol=symbol, transaction_type=Literal.SELL, quantity=qty,
        #                                 product='MIS', order_type='LIMIT', price=target_price)


    async def telegram_handler_swingtrades1(self, event, telegram_bot):
        print('{}'.format(event))
        msg = event.message.message
        print(msg)
        if not '-' in msg:
            return
        msg = msg.split(' ')
        symbol = msg[0]
        price = round(float(msg[1].split('-')[1]), 2)
        q = RabbitMQChannelSingleton()
        q.mq_channel.basic_publish(exchange='', routing_key='quotes', body=json.dumps([{'symbol': symbol, 'spot_symbol': symbol, 'price_to': price}], default=str))

    async def telegram_handler_oi_trader(self, event, telegram_bot):
        print('{}'.format(event))
        msg = event.message.message
        print(msg)
        if 'looks good for buying' not in msg or ('CE' not in msg and 'PE' not in msg):
            return
        msg = msg.replace('.', '')
        msg = msg.replace('\n', ' ')
        ce_pe = 'CE' if 'CE' in msg else 'PE'

        msg = msg.split(' ')
        mlen = len(msg)
        strike_price = None
        price = None
        for i in range(1, mlen):
            if msg[i-1].isdigit() and msg[i] == ce_pe:
                strike_price = msg[i-1]
            if msg[i-1] == 'at' and '-' in msg[i]:
                price = int(msg[i].split('-')[0])
        script = utils.get_nifty_banknifty_prefix(int(strike_price))
        symbol = utils.next_expiry_prefix(script) + strike_price + ce_pe
        # qty = self.get_order_qty(symbol, price=price, is_confident=True)
        print(strike_price, price, script, symbol)
        self.trade_observer.update(script=script, tradingsymbol=symbol, price=price)



    async def telegram_handler_retailtrades(self, event, telegram_bot):
        # print('{}'.format(event))
        msg = event.message.message
        print(msg)
        is_trade = False
        symbol = None
        price = None
        if event.photo:
            image_path = await event.download_media()
            i = Image.open(image_path)

            image_text = pytesseract.image_to_string(i)
            os.remove(image_path)
            # print(image_text)
            for l in image_text.splitlines():
                if symbol and price:
                    is_trade = True
                if (l.endswith('CE') or l.endswith('PE')):
                    line = re.sub(' +', ' ', re.sub(r'[^ \w+]', '', l))
                    line = l.split(' ')
                    ce_pe = line[-1]
                    strike_price = line[-2]
                    script = line[0]
                    symbol = utils.next_expiry_prefix(script) + strike_price + ce_pe
                    # msg = re.sub(' +', ' ', re.sub(r'[^ \w+]', '', l))
                if l.startswith('NFO'):
                    price = float(l.split(' ')[1])

        if is_trade:
            # qty = self.get_order_qty(symbol, price=price, is_confident=True)
            # print(price, symbol, qty)
            self.trade_observer.update(script=script, tradingsymbol=symbol, price=price)


    async def telegram_handler_tradelikeeagle(self, event, telegram_bot):
        CALL_PUT_KEYWORDS = ['CE', 'PE']
        # print('{}'.format(event))
        msg = event.message.message
        if not 'ONLY FOR VERY HIGH RISKY TRADER' in msg:
            return
        msg = msg.splitlines()
        trade_line = [l for l in msg if any(s in l for s in ['CE AT', 'PE AT'])][0].split()

        ce_pe_idx = [trade_line.index(x) for x in CALL_PUT_KEYWORDS if x in trade_line][0]
        ce_pe = trade_line[ce_pe_idx]
        strike_price = trade_line[ce_pe_idx-1]
        price = float(trade_line[ce_pe_idx+2])

        target_line = [l for l in msg if 'TGT' in l][0].split()
        print(target_line)
        target = float(target_line[1].split('/')[0])
        sl_line = [l for l in msg if 'SL' in l][0].split()
        sl = float(sl_line[1])
        print(strike_price, ce_pe, price, target, sl)
        script = utils.get_nifty_banknifty_prefix(int(strike_price))
        # symbol = utils.next_expiry_prefix(script) + strike_price + ce_pe
        self.trade_observer.update2(script=script, strike_price=strike_price, price=price, target=target, sl=sl)
        telegram_bot.send_message(chat_id=self.chat_id, text="Trade successful: "+" "+script+" "+str(strike_price)+" "+ce_pe)

    async def telegram_handler_stocks_fno(self, event, telegram_bot):
        msg = event.message.message
        if not msg.startswith('BUY'):
            return
        msg = msg.split()
        script = msg[1]
        strike_price = int(msg[2])
        ce_pe = msg[3]
        price = float(msg[-1])
        self.trade_observer.update2(script=script, strike_price=strike_price, ce_pe=ce_pe, price=price)
        telegram_bot.send_message(chat_id=self.chat_id, text="Trade successful: "+" "+script+" "+str(strike_price)+" "+ce_pe)

    async def telegram_handler_test123568123(self, event, telegram_bot):
        print(event.message.message)

    def get_channel_name(self, chat_id):
        channels = {1811145014: 'professor_premium', 1804015283: 'professor_debug', 2112959479: 'SwingTrades1'}
        return channels[chat_id]

    async def telegram_handler_professor_premium(self, event, telegram_bot):
        await professor_event_handler(event, telegram_bot)

    async def telegram_handler_professor_debug(self, event, telegram_bot):
        await professor_event_handler(event, telegram_bot)

    async def telegram_handler(self, event, telegram_bot):
        print('{}'.format(event))
        # print(event_msg_link)
        # import pdb; pdb.set_trace()
        telegram_channel_name = event.chat.username
        if telegram_channel_name is None:
            telegram_channel_name = self.get_channel_name(event.chat.id)
        telegram_channel_name = telegram_channel_name.lower()
        print('got_event_from: ', telegram_channel_name)
        if telegram_channel_name == 'test123568123':
            # telegram_channel_name = 'tradelikeeagle'
            telegram_channel_name = 'swingtrades1'
        # try:
        telegram_msg_handler = getattr(self, 'telegram_handler_'+telegram_channel_name)
        await telegram_msg_handler(event, telegram_bot)
        # except:
        #     event_msg_link = 'https://t.me/'+event.chat.username+'/'+str(event.id)
        #     await telegram_bot.send_message(chat_id=self.chat_id, text=event_msg_link)

