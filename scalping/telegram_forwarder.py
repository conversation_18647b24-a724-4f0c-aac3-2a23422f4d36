import telegram
import utils
from telegram_utils import TelegramUtil
from telethon import events
# from telethon import 
import asyncio
import traceback
import time
import signal
import sys
signal.signal(signal.SIGINT, lambda x, y: sys.exit(0))

# ANSHU_NEW_TELEGRAM_ID = **********
# OCTOBER_BATCH_ID = **********
# TEST_PRIVATE_CHANNEL_ID = **********
# ANSHU_TELEGRAM_ID = *********
# PROFESSOR_TEST_CHANNEL_ID = **********
# TRADER_DISCUSSION_CHANNEL_ID = **********
# BASE_RECIPIENT_ID = TRADER_DISCUSSION_CHANNEL_ID
# BANKNIFTY_NIFTY_CHAT = **********
# RECIPIENT_CHANNELS = {TRADER_DISCUSSION_CHANNEL_ID: {'forward_allowed': True},
#                       BANKNIFTY_NIFTY_CHAT: {'forward_allowed': False}
#                     }

MASTER_OPTION_TRADING_CHANNEL_ID = **********
BASE_CHANNEL_ID = MASTER_OPTION_TRADING_CHANNEL_ID
RECIPIENT_CHANNELS = {
    'mcxnsefutu': {'forward_allowed': True}, 
    # 'BNsnipersgroup': {'forward_allowed': False},
    # 'Indian_Stock_Market_Discussion': {'forward_allowed': True},
    # 'nifty_banknifty_crude': {'forward_allowed': False},
    # 'stock_market_discussions': {'forward_allowed': False},
    # 'Stock_Market_Chatting_Group': {'forward_allowed': False},
    'thestockopedia': {'forward_allowed': False},
    # 'trade_nifty_bankniftyy': {'forward_allowed': False},
    # 'NiftyMarket': {'forward_allowed': False},
    # 'discussnifty': {'forward_allowed': False},
}






if __name__ == "__main__":
    config = utils.get_config()
    telegram_client = TelegramUtil(config=config, client=True, bot=False, session_name='telegram_forwarder').client

    dialogs = [d for d in telegram_client.get_dialogs() if d.is_group]
    for d in dialogs:
        if hasattr(d.entity, 'username') and d.entity.username and d.entity.username in RECIPIENT_CHANNELS:
            print(d.entity.username)
            RECIPIENT_CHANNELS[d.entity.username]['channel_id'] = d.id
    # for message in telegram_client.iter_messages(OCTOBER_BATCH_ID, limit=1000, reverse=True):
        # print(message.id, message.text)
        # if message and message.text:
        #     telegram_client.send_message(PROFESSOR_TEST_CHANNEL_ID, message.text)
        #     time.sleep(10)

    #     msg_id_prefix = "MsgId: "+
    #     if message.text.startswith(msg_id_prefix):
    # for dialog in telegram_client.iter_dialogs():
    #     print(dialog.name, dialog.id)

    # @telegram_client.on(events.NewMessage(chats=['-100**********']))
    # async def my_event_handler(event):
    #     await telegram_client.send_message('*********', event.message.message)

    # telegram_client.send_message(ANSHU_NEW_TELEGRAM_ID, "msg")

    # telegram_client.send_message(ANSHU_NEW_TELEGRAM_ID, "This is new message MsgId", reply_to=13442)


    @telegram_client.on(events.NewMessage(chats=[BASE_CHANNEL_ID]))
    async def my_event_handler(event):
        try:
            print(event.__dict__)
            msg = event.message.message
            # print(event.message.__dict__)
            if event.message.reply_to:
                # print(event.message.reply_to.__dict__)
                reply_to_msg_id = event.message.reply_to_msg_id
                # print(reply_to_msg_id)
                reply_msg = await telegram_client.get_messages(BASE_CHANNEL_ID, ids=[reply_to_msg_id])
                reply_msg = reply_msg[0].message
                print(reply_msg)
                msg_id_prefix = "MsgId: "+ str(reply_to_msg_id)
                for channel_username, v in RECIPIENT_CHANNELS.items():
                    async for message in telegram_client.iter_messages(v['channel_id'], limit=1000):
                    # print(message.id, message.text)
                        if message and message.text == reply_msg:
                            await telegram_client.send_message(v['channel_id'], msg, reply_to=message.id)
                            break
            else:
                for channel_username,v in RECIPIENT_CHANNELS.items():
                    if v['forward_allowed']:
                        try:
                            await telegram_client.forward_messages(v['channel_id'], event.message)
                        except:
                            await telegram_client.send_message(v['channel_id'], msg)    
                    else:
                        await telegram_client.send_message(v['channel_id'], msg)

        except Exception as e:
            traceback.print_exc()

    telegram_client.run_until_disconnected()
