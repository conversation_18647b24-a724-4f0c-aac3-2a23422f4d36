from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service
import time
import os
from datetime import datetime, timed<PERSON>ta

def take_telegram_today_screenshots(channel_url, output_dir="screenshots", user_data_dir=None, headless=True):
    """
    Take multiple screenshots to capture all of today's messages in a Telegram channel (mobile view)

    Args:
        channel_url: URL of the Telegram channel
        output_dir: Directory to save the screenshots
        user_data_dir: Path to Chrome user data directory to maintain login sessions
        headless: Whether to run in headless mode (True for servers, False for local testing)
    
    Returns:
        List of screenshot file paths
    """
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)

    # Create a directory for Chrome profile if it doesn't exist
    if user_data_dir is None:
        user_data_dir = os.path.join(os.path.expanduser("~"), "telegram_selenium_profile")

    os.makedirs(user_data_dir, exist_ok=True)

    # Setup Chrome options for mobile view
    options = webdriver.ChromeOptions()

    # Headless mode for servers
    if headless:
        options.add_argument("--headless=new")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-gpu")
        options.add_argument("--remote-debugging-port=9222")
        print("🖥️ Headless mode enabled (for server environments)")
    else:
        print("🖥️ GUI mode enabled (for local testing)")

    # Mobile device emulation (iPhone 13 Pro)
    mobile_emulation = {
        "deviceMetrics": {
            "width": 375,
            "height": 812,
            "pixelRatio": 3.0
        },
        "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"
    }
    options.add_experimental_option("mobileEmulation", mobile_emulation)
    options.add_argument("--window-size=375,812")
    options.add_argument(f"--user-data-dir={user_data_dir}")
    options.add_argument("--disable-blink-features=AutomationControlled")
    options.add_experimental_option("excludeSwitches", ["enable-automation"])
    options.add_experimental_option('useAutomationExtension', False)

    # Initialize the driver
    try:
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    except Exception as e:
        print(f"Failed to initialize Chrome driver: {e}")
        print("Falling back to system ChromeDriver...")
        driver = webdriver.Chrome(options=options)

    # Set mobile viewport (if not headless)
    if not headless:
        driver.set_window_size(375, 812)
    print("📱 Mobile view enabled (375x812 - iPhone 13 Pro size)")

    screenshot_paths = []

    try:
        # Navigate to Telegram Web
        driver.get("https://web.telegram.org/a/")
        print("Navigating to Telegram Web...")

        # Check if we need to log in
        time.sleep(3)

        # Check for various login indicators
        login_required = any([
            "auth-form" in driver.page_source,
            "login" in driver.current_url.lower(),
            "auth" in driver.current_url.lower()
        ])

        if login_required:
            print("First-time login required. Please log in manually...")
            print("Waiting for login completion...")
            try:
                WebDriverWait(driver, 120).until(
                    lambda d: any([
                        d.find_elements(By.CLASS_NAME, "chat-list"),
                        d.find_elements(By.CLASS_NAME, "chatlist"),
                        d.find_elements(By.CLASS_NAME, "sidebar-left"),
                        "web.telegram.org/a/#" in d.current_url
                    ])
                )
                print("Successfully logged in! This session will be saved for future use.")
            except TimeoutException:
                print("Login timeout. Please try again.")
                return []
        else:
            print("Already logged in from previous session")

        # Navigate to the specific channel
        print(f"Navigating to channel: {channel_url}")
        navigation_successful = False

        try:
            print("Opening channel in new tab...")
            driver.execute_script(f"window.open('{channel_url}', '_blank');")
            time.sleep(5)

            if len(driver.window_handles) > 1:
                driver.switch_to.window(driver.window_handles[-1])
                time.sleep(8)

                current_url = driver.current_url
                print(f"Current URL: {current_url}")

                if channel_url.split('#')[-1] in current_url:
                    print("✓ Navigation successful")
                    navigation_successful = True
                else:
                    print("✗ Navigation failed")
                    driver.switch_to.window(driver.window_handles[0])

        except Exception as e:
            print(f"✗ Navigation failed: {e}")

        if not navigation_successful:
            print("⚠ Warning: Could not navigate to specific channel.")
            return []

        # Wait for channel content to load
        print("Waiting for channel content to load...")
        channel_loaded = False
        selectors_to_try = [
            (By.CLASS_NAME, "bubbles"),
            (By.CLASS_NAME, "bubble"),
            (By.CLASS_NAME, "messages-container"),
            (By.CLASS_NAME, "message"),
            (By.CSS_SELECTOR, "[class*='bubble']"),
            (By.CSS_SELECTOR, "[class*='message']"),
            (By.TAG_NAME, "main")
        ]

        for selector_type, selector_value in selectors_to_try:
            try:
                WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((selector_type, selector_value))
                )
                print(f"✓ Found element with selector: {selector_type} = '{selector_value}'")
                channel_loaded = True
                break
            except TimeoutException:
                continue

        if not channel_loaded:
            print("Could not find channel content.")
            return []

        print("Channel loaded successfully")

        # Find today's messages boundary
        print("🔍 Finding today's messages...")
        today_start_position = find_todays_messages_start(driver)
        
        if today_start_position is None:
            print("⚠ No messages found for today")
            return []

        print(f"✓ Found today's messages starting position: {today_start_position}")

        # Take multiple screenshots of today's messages
        screenshot_paths = capture_todays_messages_screenshots(driver, output_dir, today_start_position)

    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        import traceback
        traceback.print_exc()

    finally:
        try:
            driver.quit()
            print("Browser closed successfully")
        except Exception as e:
            print(f"Error closing browser: {e}")

    return screenshot_paths


def find_todays_messages_start(driver):
    """
    Find the scroll position where today's messages start
    Returns the scroll position or None if no today's messages found
    """
    print("Scrolling to bottom to start search...")
    
    # First scroll to the very bottom
    driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
    time.sleep(2)

    # Get today's date in various formats for matching (yesterday since it's post midnight)
    today = datetime.now() - timedelta(days=1)
    today_formats = [
        # Telegram tooltip format: "Aug 13, 2025, 11:56:10 AM"
        today.strftime("%b %d, %Y"),  # Mon DD, YYYY (e.g., "Aug 13, 2025") - PRIMARY FORMAT
        today.strftime("%B %d, %Y"),  # Month DD, YYYY (e.g., "August 13, 2025")
        today.strftime("%d %B %Y"),   # DD Month YYYY (e.g., "13 August 2025")
        today.strftime("%d %b %Y"),   # DD Mon YYYY (e.g., "13 Aug 2025")
        today.strftime("%-d %B %Y"),  # D Month YYYY (e.g., "13 August 2025" without leading zero)
        today.strftime("%-d %b %Y"),  # D Mon YYYY (e.g., "13 Aug 2025" without leading zero)
        today.strftime("%Y-%m-%d"),   # YYYY-MM-DD
        today.strftime("%d.%m.%Y"),   # DD.MM.YYYY
        today.strftime("%d/%m/%Y"),   # DD/MM/YYYY
    ]

    print(f"Looking for today's date patterns: {today_formats}")

    # Add timezone-aware patterns for IST
    ist_patterns = []
    for pattern in today_formats[:4]:  # Add IST to the main date patterns
        ist_patterns.extend([
            f"{pattern} IST",
            f"{pattern} ist",
            pattern.replace("2025", "2025 IST"),
            pattern.replace("2025", "2025 ist")
        ])
    today_formats.extend(ist_patterns)

    print(f"Extended patterns with IST: {today_formats}")

    # Simplified approach: directly target span.message-time elements and check their tooltips
    find_today_script = f"""
    const todayPatterns = {today_formats};

    console.log('Looking for patterns:', todayPatterns);

    // Find all message-time elements (these contain the timestamps)
    const messageTimeElements = document.querySelectorAll('span.message-time');
    console.log('Found', messageTimeElements.length, 'message-time elements');

    let foundTodayTimestamp = null;

    // Check each timestamp element from bottom to top (newest to oldest)
    for (let i = messageTimeElements.length - 1; i >= 0; i--) {{
        const timestampEl = messageTimeElements[i];

        // Scroll timestamp into view
        timestampEl.scrollIntoView({{ block: 'center' }});

        // Trigger hover to reveal tooltip
        const rect = timestampEl.getBoundingClientRect();
        if (rect.width > 0 && rect.height > 0) {{
            const events = ['mouseenter', 'mouseover', 'mousemove'];
            events.forEach(eventType => {{
                const event = new MouseEvent(eventType, {{
                    view: window,
                    bubbles: true,
                    cancelable: true,
                    clientX: rect.left + rect.width / 2,
                    clientY: rect.top + rect.height / 2
                }});
                timestampEl.dispatchEvent(event);
            }});
        }}

        // Small delay to let tooltip appear
        // Note: We'll check the tooltip in a separate call after all hovers
    }}

    // Return the count of timestamp elements processed
    return messageTimeElements.length;
    """

    try:
        # Execute the simplified timestamp hover script
        print("Executing timestamp hover detection...")

        timestamp_count = driver.execute_script(find_today_script)
        print(f"Processed {timestamp_count} timestamp elements")

        # Wait for hover effects to show tooltips
        time.sleep(2)

        # Now check all tooltips for today's date
        check_tooltips_script = f"""
        const todayPatterns = {today_formats};

        // Find all span.message-time elements and check their tooltips
        const messageTimeElements = document.querySelectorAll('span.message-time');
        let foundMatches = [];

        messageTimeElements.forEach((timestampEl, index) => {{
            const title = timestampEl.getAttribute('title') || '';
            const text = timestampEl.innerText || timestampEl.textContent || '';

            if (title) {{
                console.log('Checking tooltip:', title);

                // Check if this tooltip contains today's date
                const hasMatch = todayPatterns.some(pattern => title.toLowerCase().includes(pattern.toLowerCase()));
                if (hasMatch) {{
                    console.log('DATE MATCH FOUND:', title);
                    foundMatches.push({{
                        index: index,
                        title: title,
                        text: text,
                        element: timestampEl
                    }});
                }}
            }}
        }});

        if (foundMatches.length > 0) {{
            console.log('Found', foundMatches.length, 'matches for today');

            // Sort matches by their position in the DOM (earlier messages have lower index)
            foundMatches.sort((a, b) => a.index - b.index);

            // Get the first (earliest) message of today
            const firstMatch = foundMatches[0];
            console.log('First match:', firstMatch.title, 'at index', firstMatch.index);

            // Find the message container for this timestamp
            let messageContainer = firstMatch.element;
            while (messageContainer && !messageContainer.classList.contains('Message')) {{
                messageContainer = messageContainer.parentElement;
            }}

            if (messageContainer) {{
                // Scroll a bit above the first message to provide context
                const scrollOffset = Math.max(0, messageContainer.offsetTop - 100);
                window.scrollTo(0, scrollOffset);

                return {{
                    found: true,
                    position: scrollOffset,
                    matchDetails: firstMatch.title,
                    totalMatches: foundMatches.length,
                    firstMessageText: messageContainer.innerText.substring(0, 100)
                }};
            }}
        }}

        return {{ found: false, totalMatches: foundMatches.length }};
        """

        result = driver.execute_script(check_tooltips_script)
        time.sleep(2)  # Wait for smooth scroll

        if result['found']:
            print(f"✓ Found today's messages at position: {result['position']}")
            print(f"✓ First message date: {result['matchDetails']}")
            print(f"✓ First message text: {result.get('firstMessageText', 'N/A')}")
            print(f"✓ Total matches found: {result['totalMatches']}")

            # Simple approach: directly find and scroll to JSL message
            print("\n🔍 Using timestamp detection to find JSL message...")

            # Since timestamp detection already found the JSL message, let's use that information
            # to scroll directly to it
            find_jsl_by_timestamp_script = """
            // Look for the timestamp element that corresponds to 9:32 AM (JSL message time)
            const timeElements = document.querySelectorAll('span.message-time');

            for (let timeEl of timeElements) {
                const title = timeEl.getAttribute('title') || '';

                // Look for the 9:32 AM timestamp (JSL message time)
                if (title.includes('9:32:11 AM') || title.includes('9:32 AM')) {
                    console.log('Found JSL timestamp:', title);

                    // Find the message bubble containing this timestamp
                    let messageBubble = timeEl;
                    while (messageBubble && messageBubble.parentElement) {
                        messageBubble = messageBubble.parentElement;
                        if (messageBubble.classList.contains('bubble') ||
                            messageBubble.classList.contains('message')) {
                            break;
                        }
                    }

                    if (messageBubble) {
                        const messageText = messageBubble.textContent || messageBubble.innerText || '';
                        console.log('Message text:', messageText.substring(0, 100));

                        // Scroll to put JSL message at top
                        messageBubble.scrollIntoView({behavior: 'instant', block: 'start'});

                        // Fine-tune position to put it at the very top
                        setTimeout(() => {
                            const rect = messageBubble.getBoundingClientRect();
                            if (rect.top > 20) {
                                window.scrollBy(0, rect.top - 20);
                            }
                        }, 100);

                        // Highlight it
                        messageBubble.style.border = '3px solid red';
                        messageBubble.style.backgroundColor = 'yellow';

                        return {
                            found: true,
                            messageText: messageText.substring(0, 150),
                            timestamp: title
                        };
                    }
                }
            }

            return {found: false};
            """

            jsl_result = driver.execute_script(find_jsl_by_timestamp_script)
            time.sleep(3)  # Wait for scroll and positioning

            if jsl_result['found']:
                print(f"✓ Found JSL message using timestamp: {jsl_result['timestamp']}")
                print(f"✓ Message text: {jsl_result['messageText']}")

                # Wait a bit more for positioning to complete
                time.sleep(2)
                final_position = driver.execute_script("return window.pageYOffset;")
                print(f"✓ JSL message positioned at top, scroll position: {final_position}")

                # PAUSE FOR VALIDATION
                print("\n" + "="*60)
                print("🔍 MANUAL VALIDATION - JSL MESSAGE AT TOP")
                print("Check browser: JSL message should be at TOP with RED border and YELLOW background")
                print("="*60)

                user_input = input("Press Enter to continue with screenshot, or 'stop' to exit: ")
                if user_input.lower() == 'stop':
                    print("Stopping as requested.")
                    return None

                # Remove highlighting
                driver.execute_script("""
                const highlighted = document.querySelector('.bubble[style*="border: 3px solid red"]');
                if (highlighted) {
                    highlighted.style.border = '';
                    highlighted.style.backgroundColor = '';
                }
                """)

                return final_position
            else:
                print("⚠️ JSL message not found by timestamp")
                print("Using fallback...")

                return result['position']
        else:
            print(f"No date matches found (checked {result['totalMatches']} tooltips)")
        
        # Enhanced fallback: try time-based approach
        print("⚠ Date pattern search failed, trying time-based approach...")
        
        # Look for messages with today's time patterns (AM/PM times)
        time_based_script = f"""
        const messages = document.querySelectorAll('[class*="message"], [class*="bubble"], .message, .bubble');
        let recentMessage = null;
        
        // Look for recent time patterns (AM/PM)
        for (let i = messages.length - 1; i >= Math.max(0, messages.length - 50); i--) {{
            const message = messages[i];
            const text = message.innerText || message.textContent || '';
            
            // Check for AM/PM time patterns
            if (text.match(/\\d{{1,2}}:\\d{{2}}\\s*(AM|PM)/i)) {{
                recentMessage = message;
                break;
            }}
        }}
        
        if (recentMessage) {{
            recentMessage.scrollIntoView({{behavior: 'smooth', block: 'start'}});
            return recentMessage.offsetTop;
        }}
        
        return null;
        """
        
        result = driver.execute_script(time_based_script)
        time.sleep(2)
        
        if result:
            print(f"✓ Found recent messages at position: {result}")
            return result
        
        # Final fallback: assume recent messages are from today
        print("⚠ All searches failed, using recent messages fallback")
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(2)
        
        # Scroll up to capture some context
        for _ in range(10):
            driver.execute_script("window.scrollBy(0, -150);")
            time.sleep(0.3)
        
        return driver.execute_script("return window.pageYOffset;")
        
    except Exception as e:
        print(f"Error finding today's messages: {e}")
        import traceback
        traceback.print_exc()
        return None


def capture_todays_messages_screenshots(driver, output_dir, start_position):
    """
    Capture multiple screenshots of today's messages
    Returns list of screenshot file paths
    """
    screenshot_paths = []
    viewport_height = 812  # iPhone 13 Pro height
    overlap_pixels = 100   # Overlap between screenshots
    scroll_step = viewport_height - overlap_pixels
    
    print(f"📸 Starting screenshot capture from position {start_position}")

    # Navigate to the start position - try multiple scroll methods
    print(f"🔄 Attempting to scroll to position {start_position}")

    # Method 1: Try scrolling the window
    driver.execute_script(f"window.scrollTo(0, {start_position});")
    time.sleep(1)

    # Check if that worked
    actual_position = driver.execute_script("return window.pageYOffset;")
    print(f"🔍 Method 1 - Window scroll: Requested {start_position}, Actual {actual_position}")

    if actual_position < start_position * 0.8:  # If we're not close to target
        # Method 2: Try finding and scrolling the chat container
        try:
            chat_container = driver.find_element(By.CSS_SELECTOR, '.messages-container, .chat-container, [class*="messages"], [class*="chat"]')
            driver.execute_script(f"arguments[0].scrollTop = {start_position};", chat_container)
            time.sleep(1)
            actual_position = driver.execute_script("return arguments[0].scrollTop;", chat_container)
            print(f"🔍 Method 2 - Container scroll: Requested {start_position}, Actual {actual_position}")
        except Exception as e:
            print(f"⚠️ Method 2 failed: {e}")

            # Method 3: Try scrolling by finding a specific message element
            try:
                # Find all message elements and scroll to approximately the right one
                messages = driver.find_elements(By.CSS_SELECTOR, '.bubble, [data-testid="message"], .message')
                if messages and len(messages) > 10:
                    # Estimate which message to scroll to based on position
                    target_index = min(len(messages) - 5, max(0, int(len(messages) * 0.1)))  # Start near top
                    target_message = messages[target_index]
                    driver.execute_script("arguments[0].scrollIntoView({behavior: 'instant', block: 'start'});", target_message)
                    time.sleep(1)
                    print(f"🔍 Method 3 - Scrolled to message index {target_index}")
                else:
                    print("⚠️ Method 3 failed: No messages found")
            except Exception as e:
                print(f"⚠️ Method 3 failed: {e}")

    # Final position check
    final_position = driver.execute_script("return window.pageYOffset;")
    print(f"🔍 Final scroll position: {final_position}")

    # Double-check what messages are visible at this position
    try:
        messages = driver.find_elements(By.CSS_SELECTOR, '.bubble, [data-testid="message"], .message')
        if messages:
            first_visible_msg = messages[0]
            msg_text = first_visible_msg.text[:100] if first_visible_msg.text else "No text"
            print(f"🔍 First visible message: {msg_text}")
    except Exception as e:
        print(f"⚠️ Could not check visible messages: {e}")
    
    screenshot_count = 0
    max_screenshots = 20  # Safety limit
    last_scroll_position = start_position
    
    while screenshot_count < max_screenshots:
        screenshot_count += 1
        
        # Take screenshot
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        screenshot_path = os.path.join(output_dir, f"telegram_today_{screenshot_count:03d}_{timestamp}.png")
        
        try:
            success = driver.save_screenshot(screenshot_path)
            if success:
                file_size = os.path.getsize(screenshot_path)
                print(f"✓ Screenshot {screenshot_count} saved: {screenshot_path} ({file_size} bytes)")
                screenshot_paths.append(screenshot_path)
            else:
                print(f"✗ Failed to save screenshot {screenshot_count}")
                break
                
        except Exception as e:
            print(f"✗ Error taking screenshot {screenshot_count}: {e}")
            break
        
        # Check if we've reached the bottom
        current_position = driver.execute_script("return window.pageYOffset;")
        max_scroll = driver.execute_script("return document.body.scrollHeight - window.innerHeight;")
        
        if current_position >= max_scroll:
            print("✓ Reached bottom of page")
            break
        
        # Scroll down for next screenshot
        new_position = current_position + scroll_step
        driver.execute_script(f"window.scrollTo(0, {new_position});")
        time.sleep(1)
        
        # Check if scroll actually moved (prevents infinite loop)
        actual_position = driver.execute_script("return window.pageYOffset;")
        if abs(actual_position - last_scroll_position) < 10:
            print("✓ No more scrolling possible")
            break
            
        last_scroll_position = actual_position
        
        # Additional check: see if we've moved past today's messages
        # This is a simple heuristic - you might want to make it more sophisticated
        if screenshot_count > 1:
            time.sleep(0.5)  # Brief pause between screenshots
    
    print(f"📸 Captured {len(screenshot_paths)} screenshots of today's messages")
    return screenshot_paths


def main():
    """Main function to run the screenshot tool"""
    print("=== Telegram Today's Messages Screenshot Tool ===")
    print()

    # Default channel URL
    channel_url = "https://web.telegram.org/a/#-1002075758731"

    # Auto-detect environment
    is_server = (
        os.environ.get('SSH_CONNECTION') is not None or
        os.environ.get('CI') is not None or
        os.environ.get('HEADLESS') == 'true'
    )

    headless = is_server
    print(f"🖥️ {'Server' if is_server else 'Local'} environment detected - using {'headless' if headless else 'GUI'} mode")

    print(f"Taking screenshots of today's messages from: {channel_url}")
    print()
    
    output_dir = os.path.join(os.path.expanduser("~"), "screenshots")
    
    try:
        screenshot_paths = take_telegram_today_screenshots(
            channel_url=channel_url, 
            output_dir=output_dir, 
            headless=headless
        )
        
        if screenshot_paths:
            print(f"\n✓ Successfully captured {len(screenshot_paths)} screenshots:")
            for i, path in enumerate(screenshot_paths, 1):
                print(f"  {i}. {path}")
        else:
            print("\n⚠ No screenshots were captured")
            
        print("\n=== Screenshot process completed ===")
        
    except KeyboardInterrupt:
        print("\n=== Process interrupted by user ===")
    except Exception as e:
        print(f"\n=== Error occurred: {e} ===")

if __name__ == "__main__":
    main()
