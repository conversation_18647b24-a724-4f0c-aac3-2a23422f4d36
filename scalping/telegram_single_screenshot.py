from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service
import time
import os
from datetime import datetime, timed<PERSON>ta

def take_telegram_today_screenshots(channel_url, output_dir="screenshots", user_data_dir=None, headless=True):
    """
    Take multiple screenshots to capture all of today's messages in a Telegram channel (mobile view)

    Args:
        channel_url: URL of the Telegram channel
        output_dir: Directory to save the screenshots
        user_data_dir: Path to Chrome user data directory to maintain login sessions
        headless: Whether to run in headless mode (True for servers, False for local testing)
    
    Returns:
        List of screenshot file paths
    """
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)

    # Create a directory for Chrome profile if it doesn't exist
    if user_data_dir is None:
        user_data_dir = os.path.join(os.path.expanduser("~"), "telegram_selenium_profile")

    os.makedirs(user_data_dir, exist_ok=True)

    # Setup Chrome options for mobile view
    options = webdriver.ChromeOptions()

    # Headless mode for servers
    if headless:
        options.add_argument("--headless=new")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-gpu")
        options.add_argument("--remote-debugging-port=9222")
        print("🖥️ Headless mode enabled (for server environments)")
    else:
        print("🖥️ GUI mode enabled (for local testing)")

    # Mobile device emulation (iPhone 13 Pro)
    mobile_emulation = {
        "deviceMetrics": {
            "width": 375,
            "height": 812,
            "pixelRatio": 3.0
        },
        "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"
    }
    options.add_experimental_option("mobileEmulation", mobile_emulation)
    options.add_argument("--window-size=375,812")
    options.add_argument(f"--user-data-dir={user_data_dir}")
    options.add_argument("--disable-blink-features=AutomationControlled")
    options.add_experimental_option("excludeSwitches", ["enable-automation"])
    options.add_experimental_option('useAutomationExtension', False)

    # Initialize the driver
    try:
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    except Exception as e:
        print(f"Failed to initialize Chrome driver: {e}")
        print("Falling back to system ChromeDriver...")
        driver = webdriver.Chrome(options=options)

    # Set mobile viewport (if not headless)
    if not headless:
        driver.set_window_size(375, 812)
    print("📱 Mobile view enabled (375x812 - iPhone 13 Pro size)")

    screenshot_paths = []

    try:
        # Navigate to Telegram Web
        driver.get("https://web.telegram.org/a/")
        print("Navigating to Telegram Web...")

        # Check if we need to log in
        time.sleep(3)

        # Check for various login indicators
        login_required = any([
            "auth-form" in driver.page_source,
            "login" in driver.current_url.lower(),
            "auth" in driver.current_url.lower()
        ])

        if login_required:
            print("First-time login required. Please log in manually...")
            print("Waiting for login completion...")
            try:
                WebDriverWait(driver, 120).until(
                    lambda d: any([
                        d.find_elements(By.CLASS_NAME, "chat-list"),
                        d.find_elements(By.CLASS_NAME, "chatlist"),
                        d.find_elements(By.CLASS_NAME, "sidebar-left"),
                        "web.telegram.org/a/#" in d.current_url
                    ])
                )
                print("Successfully logged in! This session will be saved for future use.")
            except TimeoutException:
                print("Login timeout. Please try again.")
                return []
        else:
            print("Already logged in from previous session")

        # Navigate to the specific channel
        print(f"Navigating to channel: {channel_url}")
        navigation_successful = False

        try:
            print("Opening channel in new tab...")
            driver.execute_script(f"window.open('{channel_url}', '_blank');")
            time.sleep(5)

            if len(driver.window_handles) > 1:
                driver.switch_to.window(driver.window_handles[-1])
                time.sleep(8)

                current_url = driver.current_url
                print(f"Current URL: {current_url}")

                if channel_url.split('#')[-1] in current_url:
                    print("✓ Navigation successful")
                    navigation_successful = True
                else:
                    print("✗ Navigation failed")
                    driver.switch_to.window(driver.window_handles[0])

        except Exception as e:
            print(f"✗ Navigation failed: {e}")

        if not navigation_successful:
            print("⚠ Warning: Could not navigate to specific channel.")
            return []

        # Wait for channel content to load
        print("Waiting for channel content to load...")
        channel_loaded = False
        selectors_to_try = [
            (By.CLASS_NAME, "bubbles"),
            (By.CLASS_NAME, "bubble"),
            (By.CLASS_NAME, "messages-container"),
            (By.CLASS_NAME, "message"),
            (By.CSS_SELECTOR, "[class*='bubble']"),
            (By.CSS_SELECTOR, "[class*='message']"),
            (By.TAG_NAME, "main")
        ]

        for selector_type, selector_value in selectors_to_try:
            try:
                WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((selector_type, selector_value))
                )
                print(f"✓ Found element with selector: {selector_type} = '{selector_value}'")
                channel_loaded = True
                break
            except TimeoutException:
                continue

        if not channel_loaded:
            print("Could not find channel content.")
            return []

        print("Channel loaded successfully")

        # Find today's messages boundary
        print("🔍 Finding today's messages...")
        today_start_position = find_todays_messages_start(driver)
        
        if today_start_position is None:
            print("⚠ No messages found for today")
            return []

        print(f"✓ Found today's messages starting position: {today_start_position}")

        # Take multiple screenshots of today's messages
        screenshot_paths = capture_todays_messages_screenshots(driver, output_dir, today_start_position)

    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        import traceback
        traceback.print_exc()

    finally:
        try:
            driver.quit()
            print("Browser closed successfully")
        except Exception as e:
            print(f"Error closing browser: {e}")

    return screenshot_paths


def find_todays_messages_start(driver):
    """
    Find the scroll position where today's messages start
    Returns the scroll position or None if no today's messages found
    """
    print("Scrolling to bottom to start search...")
    
    # First scroll to the very bottom
    driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
    time.sleep(2)

    # Get today's date in various formats for matching (yesterday since it's post midnight)
    today = datetime.now() - timedelta(days=1)
    today_formats = [
        today.strftime("%d %B %Y"),  # DD Month YYYY (e.g., "13 August 2025")
        today.strftime("%d %b %Y"),  # DD Mon YYYY (e.g., "13 Aug 2025")
        today.strftime("%-d %B %Y"), # D Month YYYY (e.g., "13 August 2025" without leading zero)
        today.strftime("%-d %b %Y"), # D Mon YYYY (e.g., "13 Aug 2025" without leading zero)
        today.strftime("%b %d, %Y"), # Mon DD, YYYY (e.g., "Aug 13, 2025")
        today.strftime("%B %d, %Y"), # Month DD, YYYY (e.g., "August 13, 2025")
        today.strftime("%Y-%m-%d"),  # YYYY-MM-DD
        today.strftime("%d.%m.%Y"),  # DD.MM.YYYY
        today.strftime("%d/%m/%Y"),  # DD/MM/YYYY
        # Add timezone-aware formats
        f"{today.strftime('%d %B %Y')} IST",  # DD Month YYYY IST
        f"{today.strftime('%-d %B %Y')} IST", # D Month YYYY IST
    ]

    print(f"Looking for today's date patterns: {today_formats}")

    # Add timezone-aware patterns for IST
    ist_patterns = []
    for pattern in today_formats[:4]:  # Add IST to the main date patterns
        ist_patterns.extend([
            f"{pattern} IST",
            f"{pattern} ist",
            pattern.replace("2025", "2025 IST"),
            pattern.replace("2025", "2025 ist")
        ])
    today_formats.extend(ist_patterns)

    print(f"Extended patterns with IST: {today_formats}")

    # Enhanced JavaScript to find today's messages by hovering over timestamp elements
    find_today_script = f"""
    const todayPatterns = {today_formats};

    console.log('Looking for patterns:', todayPatterns);

    // Find actual message bubbles that contain trading alerts
    const allElements = document.querySelectorAll('*');
    const messageBubbles = [];

    allElements.forEach(element => {{
        const text = element.innerText || element.textContent || '';
        if (text.includes('Alert:') && text.includes('@') && text.length < 200) {{
            messageBubbles.push(element);
        }}
    }});

    console.log('Found', messageBubbles.length, 'message bubbles with trading alerts');

    // Function to hover over timestamp elements and check for date
    function checkMessageForDate(messageElement) {{
        // Find all potential timestamp elements within the message
        const timestampSelectors = [
            '[class*="time"]',
            '[class*="date"]',
            '[class*="timestamp"]',
            '.time',
            '.date',
            'time',
            '[title*=":"]',
            '[title*="AM"]',
            '[title*="PM"]'
        ];

        let timestampElements = [];

        // Look for timestamp elements in the message and its parent/siblings
        let searchElements = [messageElement];
        if (messageElement.parentElement) {{
            searchElements.push(messageElement.parentElement);
            // Also check siblings
            const siblings = messageElement.parentElement.children;
            for (let sibling of siblings) {{
                searchElements.push(sibling);
            }}
        }}

        searchElements.forEach(searchEl => {{
            timestampSelectors.forEach(selector => {{
                const elements = searchEl.querySelectorAll(selector);
                elements.forEach(el => {{
                    if (!timestampElements.includes(el)) {{
                        timestampElements.push(el);
                    }}
                }});
            }});
        }});

        console.log('Found', timestampElements.length, 'timestamp elements for message:', messageElement.innerText.substring(0, 50));

        // Hover over each timestamp element and check for date tooltip
        let foundDate = false;
        timestampElements.forEach(timestampEl => {{
            // Scroll timestamp into view
            timestampEl.scrollIntoView({{ block: 'center' }});

            // Trigger hover events on timestamp
            const rect = timestampEl.getBoundingClientRect();
            if (rect.width > 0 && rect.height > 0) {{
                const events = ['mouseenter', 'mouseover', 'mousemove'];
                events.forEach(eventType => {{
                    const event = new MouseEvent(eventType, {{
                        view: window,
                        bubbles: true,
                        cancelable: true,
                        clientX: rect.left + rect.width / 2,
                        clientY: rect.top + rect.height / 2
                    }});
                    timestampEl.dispatchEvent(event);
                }});

                // Check for tooltip after hover
                setTimeout(() => {{
                    const title = timestampEl.getAttribute('title') || '';
                    const text = timestampEl.innerText || timestampEl.textContent || '';
                    const allText = (title + ' ' + text).toLowerCase();

                    console.log('Timestamp element text/title:', allText);

                    // Check if this contains today's date
                    const hasDate = todayPatterns.some(pattern => allText.includes(pattern.toLowerCase()));
                    if (hasDate) {{
                        console.log('FOUND DATE MATCH in timestamp:', allText);
                        foundDate = true;
                    }}
                }}, 50);
            }}
        }});

        return foundDate;
    }}

    // Check messages from bottom to top
    let foundTodayMessage = null;

    for (let i = messageBubbles.length - 1; i >= 0; i--) {{
        const message = messageBubbles[i];

        // Scroll message into view
        message.scrollIntoView({{ block: 'center' }});

        // Check this message for today's date
        if (checkMessageForDate(message)) {{
            foundTodayMessage = message;
            console.log('Found today message:', message.innerText.substring(0, 100));
            break; // Found the first (earliest) today message
        }}
    }}

    if (foundTodayMessage) {{
        foundTodayMessage.scrollIntoView({{behavior: 'smooth', block: 'start'}});
        return foundTodayMessage.offsetTop;
    }}

    console.log('No today messages found');
    return null;
    """

    try:
        # Execute the script to find today's messages with proper timing
        print("Executing timestamp hover detection...")

        # First, let's identify message bubbles and timestamp elements
        identify_script = f"""
        const todayPatterns = {today_formats};

        // Find message bubbles with trading alerts
        const allElements = document.querySelectorAll('*');
        const messageBubbles = [];

        allElements.forEach(element => {{
            const text = element.innerText || element.textContent || '';
            if (text.includes('Alert:') && text.includes('@') && text.length < 200) {{
                messageBubbles.push({{
                    element: element,
                    text: text.substring(0, 100)
                }});
            }}
        }});

        console.log('Found', messageBubbles.length, 'message bubbles');
        return messageBubbles.length;
        """

        bubble_count = driver.execute_script(identify_script)
        print(f"Found {bubble_count} message bubbles")

        if bubble_count == 0:
            print("No message bubbles found")
            return None

        # Now check each message bubble for timestamps with proper delays
        for i in range(min(10, bubble_count)):  # Check last 10 messages
            print(f"Checking message {i+1}/{min(10, bubble_count)}...")

            check_message_script = f"""
            const todayPatterns = {today_formats};
            const allElements = document.querySelectorAll('*');
            const messageBubbles = [];

            allElements.forEach(element => {{
                const text = element.innerText || element.textContent || '';
                if (text.includes('Alert:') && text.includes('@') && text.length < 200) {{
                    messageBubbles.push(element);
                }}
            }});

            const messageIndex = messageBubbles.length - 1 - {i};
            if (messageIndex < 0 || messageIndex >= messageBubbles.length) return null;

            const message = messageBubbles[messageIndex];
            message.scrollIntoView({{ block: 'center' }});

            // Find timestamp elements
            const timestampSelectors = ['[class*="time"]', '[class*="date"]', 'time', '[title*=":"]'];
            let timestampElements = [];

            // Search in message and parent elements
            let searchElements = [message];
            if (message.parentElement) {{
                searchElements.push(message.parentElement);
                const siblings = Array.from(message.parentElement.children);
                searchElements.push(...siblings);
            }}

            searchElements.forEach(searchEl => {{
                timestampSelectors.forEach(selector => {{
                    const elements = searchEl.querySelectorAll(selector);
                    elements.forEach(el => {{
                        if (!timestampElements.includes(el)) {{
                            timestampElements.push(el);
                        }}
                    }});
                }});
            }});

            console.log('Found', timestampElements.length, 'timestamp elements');

            // Hover over each timestamp element
            timestampElements.forEach(timestampEl => {{
                const rect = timestampEl.getBoundingClientRect();
                if (rect.width > 0 && rect.height > 0) {{
                    const event = new MouseEvent('mouseover', {{
                        view: window,
                        bubbles: true,
                        cancelable: true,
                        clientX: rect.left + rect.width / 2,
                        clientY: rect.top + rect.height / 2
                    }});
                    timestampEl.dispatchEvent(event);
                }}
            }});

            return {{
                messageText: message.innerText.substring(0, 100),
                timestampCount: timestampElements.length
            }};
            """

            message_info = driver.execute_script(check_message_script)
            if message_info:
                print(f"  Message: {message_info['messageText']}")
                print(f"  Timestamps: {message_info['timestampCount']}")

            # Wait for hover effects to show tooltips
            time.sleep(1)

            # Check for date matches after hover
            check_tooltips_script = f"""
            const todayPatterns = {today_formats};
            const allElements = document.querySelectorAll('*');

            let foundMatch = false;
            let matchDetails = '';

            allElements.forEach(element => {{
                const title = element.getAttribute('title') || '';
                const text = element.innerText || element.textContent || '';
                const allText = (title + ' ' + text).toLowerCase();

                if (title && (title.includes('August') || title.includes('2025') || title.includes('AM') || title.includes('PM'))) {{
                    console.log('Found tooltip:', title);

                    const hasMatch = todayPatterns.some(pattern => allText.includes(pattern.toLowerCase()));
                    if (hasMatch) {{
                        foundMatch = true;
                        matchDetails = title;
                        console.log('DATE MATCH FOUND:', title);
                    }}
                }}
            }});

            return {{ foundMatch: foundMatch, matchDetails: matchDetails }};
            """

            tooltip_result = driver.execute_script(check_tooltips_script)

            if tooltip_result['foundMatch']:
                print(f"✓ Found date match: {tooltip_result['matchDetails']}")

                # Get the position of this message
                position_script = f"""
                const allElements = document.querySelectorAll('*');
                const messageBubbles = [];

                allElements.forEach(element => {{
                    const text = element.innerText || element.textContent || '';
                    if (text.includes('Alert:') && text.includes('@') && text.length < 200) {{
                        messageBubbles.push(element);
                    }}
                }});

                const messageIndex = messageBubbles.length - 1 - {i};
                if (messageIndex >= 0 && messageIndex < messageBubbles.length) {{
                    const message = messageBubbles[messageIndex];
                    message.scrollIntoView({{behavior: 'smooth', block: 'start'}});
                    return message.offsetTop;
                }}
                return null;
                """

                position = driver.execute_script(position_script)
                time.sleep(2)  # Wait for smooth scroll

                if position:
                    print(f"✓ Found today's messages at position: {position}")
                    return position

        print("No date matches found in recent messages")
        
        # Enhanced fallback: try time-based approach
        print("⚠ Date pattern search failed, trying time-based approach...")
        
        # Look for messages with today's time patterns (AM/PM times)
        time_based_script = f"""
        const messages = document.querySelectorAll('[class*="message"], [class*="bubble"], .message, .bubble');
        let recentMessage = null;
        
        // Look for recent time patterns (AM/PM)
        for (let i = messages.length - 1; i >= Math.max(0, messages.length - 50); i--) {{
            const message = messages[i];
            const text = message.innerText || message.textContent || '';
            
            // Check for AM/PM time patterns
            if (text.match(/\\d{{1,2}}:\\d{{2}}\\s*(AM|PM)/i)) {{
                recentMessage = message;
                break;
            }}
        }}
        
        if (recentMessage) {{
            recentMessage.scrollIntoView({{behavior: 'smooth', block: 'start'}});
            return recentMessage.offsetTop;
        }}
        
        return null;
        """
        
        result = driver.execute_script(time_based_script)
        time.sleep(2)
        
        if result:
            print(f"✓ Found recent messages at position: {result}")
            return result
        
        # Final fallback: assume recent messages are from today
        print("⚠ All searches failed, using recent messages fallback")
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(2)
        
        # Scroll up to capture some context
        for _ in range(10):
            driver.execute_script("window.scrollBy(0, -150);")
            time.sleep(0.3)
        
        return driver.execute_script("return window.pageYOffset;")
        
    except Exception as e:
        print(f"Error finding today's messages: {e}")
        import traceback
        traceback.print_exc()
        return None


def capture_todays_messages_screenshots(driver, output_dir, start_position):
    """
    Capture multiple screenshots of today's messages
    Returns list of screenshot file paths
    """
    screenshot_paths = []
    viewport_height = 812  # iPhone 13 Pro height
    overlap_pixels = 100   # Overlap between screenshots
    scroll_step = viewport_height - overlap_pixels
    
    print(f"📸 Starting screenshot capture from position {start_position}")
    
    # Navigate to the start position
    driver.execute_script(f"window.scrollTo(0, {start_position});")
    time.sleep(2)
    
    screenshot_count = 0
    max_screenshots = 20  # Safety limit
    last_scroll_position = start_position
    
    while screenshot_count < max_screenshots:
        screenshot_count += 1
        
        # Take screenshot
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        screenshot_path = os.path.join(output_dir, f"telegram_today_{screenshot_count:03d}_{timestamp}.png")
        
        try:
            success = driver.save_screenshot(screenshot_path)
            if success:
                file_size = os.path.getsize(screenshot_path)
                print(f"✓ Screenshot {screenshot_count} saved: {screenshot_path} ({file_size} bytes)")
                screenshot_paths.append(screenshot_path)
            else:
                print(f"✗ Failed to save screenshot {screenshot_count}")
                break
                
        except Exception as e:
            print(f"✗ Error taking screenshot {screenshot_count}: {e}")
            break
        
        # Check if we've reached the bottom
        current_position = driver.execute_script("return window.pageYOffset;")
        max_scroll = driver.execute_script("return document.body.scrollHeight - window.innerHeight;")
        
        if current_position >= max_scroll:
            print("✓ Reached bottom of page")
            break
        
        # Scroll down for next screenshot
        new_position = current_position + scroll_step
        driver.execute_script(f"window.scrollTo(0, {new_position});")
        time.sleep(1)
        
        # Check if scroll actually moved (prevents infinite loop)
        actual_position = driver.execute_script("return window.pageYOffset;")
        if abs(actual_position - last_scroll_position) < 10:
            print("✓ No more scrolling possible")
            break
            
        last_scroll_position = actual_position
        
        # Additional check: see if we've moved past today's messages
        # This is a simple heuristic - you might want to make it more sophisticated
        if screenshot_count > 1:
            time.sleep(0.5)  # Brief pause between screenshots
    
    print(f"📸 Captured {len(screenshot_paths)} screenshots of today's messages")
    return screenshot_paths


def main():
    """Main function to run the screenshot tool"""
    print("=== Telegram Today's Messages Screenshot Tool ===")
    print()

    # Default channel URL
    channel_url = "https://web.telegram.org/a/#-1002075758731"

    # Auto-detect environment
    is_server = (
        os.environ.get('SSH_CONNECTION') is not None or
        os.environ.get('CI') is not None or
        os.environ.get('HEADLESS') == 'true'
    )

    headless = is_server
    print(f"🖥️ {'Server' if is_server else 'Local'} environment detected - using {'headless' if headless else 'GUI'} mode")

    print(f"Taking screenshots of today's messages from: {channel_url}")
    print()
    
    output_dir = os.path.join(os.path.expanduser("~"), "screenshots")
    
    try:
        screenshot_paths = take_telegram_today_screenshots(
            channel_url=channel_url, 
            output_dir=output_dir, 
            headless=headless
        )
        
        if screenshot_paths:
            print(f"\n✓ Successfully captured {len(screenshot_paths)} screenshots:")
            for i, path in enumerate(screenshot_paths, 1):
                print(f"  {i}. {path}")
        else:
            print("\n⚠ No screenshots were captured")
            
        print("\n=== Screenshot process completed ===")
        
    except KeyboardInterrupt:
        print("\n=== Process interrupted by user ===")
    except Exception as e:
        print(f"\n=== Error occurred: {e} ===")

if __name__ == "__main__":
    main()
