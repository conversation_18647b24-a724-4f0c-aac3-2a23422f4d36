from trade import Trade
from constants import Literal
import os

class Config:
    def __init__(self, myconfig):
        self.__myconfig = myconfig
        self.db_host = myconfig['db_host']
        self.db_name = myconfig['db_name']
        self.db_user = myconfig['db_user']
        self.db_port = myconfig['db_port']

        self.zerodha_username = myconfig['zerodha_username']
        self.zerodha_password = myconfig['zerodha_password']
        self.zerodha_api_key = myconfig['zerodha_api_key']
        self.zerodha_api_secret = myconfig['zerodha_api_secret']
        self.zerodha_access_token = myconfig['zerodha_access_token']

        self.zerodha_secret = myconfig['zerodha_secret']
        self.zerodha_enctoken = os.environ.get('ENCTOKEN', myconfig['zerodha_enctoken'])
        self.zerodha_public_token = myconfig['zerodha_public_token']

        self.index = myconfig.get('index', Literal.BANKNIFTY)
        self.index_fut = myconfig.get('index_fut')
        self.quantity = myconfig['quantity']
        self.capital = myconfig['capital']
        self.exchange = Literal.NFO
        self.transaction = myconfig.get('transaction_type', Literal.BUY)
        self.sl = myconfig['sl']
        self.twitter_bearer_token = myconfig['twitter_bearer_token']
        self.twitter_consumer_key = myconfig['twitter_consumer_key']
        self.twitter_consumer_secret = myconfig['twitter_consumer_secret']
        self.twitter_access_token = myconfig['twitter_access_token']
        self.twitter_access_token_secret = myconfig['twitter_access_token_secret']
        self.telegram_api_id = myconfig['telegram_api_id']
        self.telegram_api_hash = myconfig['telegram_api_hash']
        self.telegram_recommendation_channels = myconfig['telegram_recommendation_channels']
        self.telegram_bot_userid = myconfig['telegram_bot_userid']
        self.telegram_bot_token = myconfig['telegram_bot_token']
        self.telegram_phone_number = myconfig['telegram_phone_number']
        self.twitter_recos_to_telegram_channel = myconfig['twitter_recos_to_telegram_channel']

        # self.kotak_userid = myconfig['kotak_userid']
        # self.kotak_access_token = myconfig['kotak_access_token']
        # self.kotak_pass = myconfig['kotak_pass']
        # self.kotak_consumer_key = myconfig['kotak_consumer_key']
        # self.kotak_consumer_secret = myconfig['kotak_consumer_secret']
        # self.kotak_app_id = myconfig['kotak_app_id']
        # self.kotak_api_host = myconfig['kotak_api_host']
        # self.kotak_child_account_configs = []
        # for acc in myconfig['kotak_child_accounts']:
        #     k = KotakAccount(userid=acc['kotak_userid'], password=acc['kotak_pass'], access_token=acc['kotak_access_token'], consumer_key=acc['kotak_consumer_key'], consumer_secret=acc['kotak_consumer_secret'])
        #     self.kotak_child_account_configs.append(k)

    def __repr__(self) -> str:
        return str(self.__myconfig)
