from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
import time
import os

def explore_telegram_dom():
    """Explore the DOM structure to find the correct selectors for messages"""
    
    # Setup Chrome options
    options = webdriver.ChromeOptions()
    mobile_emulation = {
        "deviceMetrics": {"width": 375, "height": 812, "pixelRatio": 3.0},
        "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"
    }
    options.add_experimental_option("mobileEmulation", mobile_emulation)
    options.add_argument("--window-size=375,812")
    
    user_data_dir = os.path.join(os.path.expanduser("~"), "telegram_selenium_profile")
    options.add_argument(f"--user-data-dir={user_data_dir}")
    
    driver = webdriver.Chrome(options=options)

    try:
        # Navigate to Telegram channel
        driver.get("https://web.telegram.org/a/#-1002075758731")
        time.sleep(5)
        
        print("Exploring DOM structure...")
        
        # Script to explore the page structure
        explore_script = """
        // Find all elements that might contain messages
        const allElements = document.querySelectorAll('*');
        
        // Look for elements that contain trading alert text
        const tradingAlerts = [];
        allElements.forEach(element => {
            const text = element.innerText || element.textContent || '';
            if (text.includes('Alert:') && text.includes('@')) {
                tradingAlerts.push({
                    tagName: element.tagName,
                    className: element.className,
                    id: element.id,
                    text: text.substring(0, 100),
                    parentClassName: element.parentElement ? element.parentElement.className : '',
                    parentTagName: element.parentElement ? element.parentElement.tagName : ''
                });
            }
        });
        
        // Also look for elements with time-like patterns
        const timeElements = [];
        allElements.forEach(element => {
            const text = element.innerText || element.textContent || '';
            const title = element.getAttribute('title') || '';
            
            if (text.match(/\\d{1,2}:\\d{2}\\s*(AM|PM)/i) || 
                title.match(/\\d{1,2}:\\d{2}\\s*(AM|PM)/i) ||
                text.includes('11:') || title.includes('11:')) {
                timeElements.push({
                    tagName: element.tagName,
                    className: element.className,
                    id: element.id,
                    text: text.substring(0, 50),
                    title: title,
                    parentClassName: element.parentElement ? element.parentElement.className : '',
                    parentTagName: element.parentElement ? element.parentElement.tagName : ''
                });
            }
        });
        
        // Find all elements with 'bubble' in their class
        const bubbleElements = [];
        allElements.forEach(element => {
            if (element.className && element.className.includes('bubble')) {
                bubbleElements.push({
                    tagName: element.tagName,
                    className: element.className,
                    id: element.id,
                    text: (element.innerText || element.textContent || '').substring(0, 100),
                    childCount: element.children.length
                });
            }
        });
        
        return {
            tradingAlerts: tradingAlerts,
            timeElements: timeElements,
            bubbleElements: bubbleElements
        };
        """
        
        results = driver.execute_script(explore_script)
        
        print("\n=== TRADING ALERT ELEMENTS ===")
        for alert in results['tradingAlerts']:
            print(f"Tag: {alert['tagName']}, Class: {alert['className']}")
            print(f"Parent: {alert['parentTagName']}.{alert['parentClassName']}")
            print(f"Text: {alert['text']}")
            print("-" * 40)
        
        print("\n=== TIME ELEMENTS ===")
        for time_elem in results['timeElements']:
            print(f"Tag: {time_elem['tagName']}, Class: {time_elem['className']}")
            print(f"Parent: {time_elem['parentTagName']}.{time_elem['parentClassName']}")
            print(f"Text: {time_elem['text']}")
            print(f"Title: {time_elem['title']}")
            print("-" * 40)
        
        print("\n=== BUBBLE ELEMENTS ===")
        for bubble in results['bubbleElements']:
            print(f"Tag: {bubble['tagName']}, Class: {bubble['className']}")
            print(f"Text: {bubble['text']}")
            print(f"Children: {bubble['childCount']}")
            print("-" * 40)
        
        # Now try to find the actual message container
        print("\n=== FINDING MESSAGE CONTAINERS ===")
        container_script = """
        // Look for the main chat/messages container
        const possibleContainers = [
            document.querySelector('.messages-container'),
            document.querySelector('[class*="messages"]'),
            document.querySelector('.chat'),
            document.querySelector('[class*="chat"]'),
            document.querySelector('.bubbles'),
            document.querySelector('[class*="bubbles"]'),
            document.querySelector('main'),
            document.querySelector('[role="main"]')
        ];
        
        let containerInfo = [];
        possibleContainers.forEach((container, index) => {
            if (container) {
                const children = container.children;
                let messageCount = 0;
                
                // Count children that might be messages
                for (let child of children) {
                    const text = child.innerText || child.textContent || '';
                    if (text.includes('Alert:') || text.length > 10) {
                        messageCount++;
                    }
                }
                
                containerInfo.push({
                    index: index,
                    tagName: container.tagName,
                    className: container.className,
                    childrenCount: children.length,
                    messageCount: messageCount,
                    sampleText: (container.innerText || '').substring(0, 200)
                });
            }
        });
        
        return containerInfo;
        """
        
        containers = driver.execute_script(container_script)
        for container in containers:
            print(f"Container {container['index']}: {container['tagName']}.{container['className']}")
            print(f"  Children: {container['childrenCount']}, Messages: {container['messageCount']}")
            print(f"  Sample: {container['sampleText']}")
            print("-" * 40)
        
    finally:
        input("Press Enter to close browser...")
        driver.quit()

if __name__ == "__main__":
    explore_telegram_dom()
