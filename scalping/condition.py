from constants import Literal


class Condition:
    def __init__(self, cond_str) -> None:
        cond_str = cond_str.replace(' ', '')
        self.index, self.operator, self.price = None, None, None
        if cond_str.startswith(Literal.NIFTY):
            self.index, self.operator, self.price = Literal.NIFTY, cond_str[len(
                Literal.NIFTY):len(Literal.NIFTY)+1], float(cond_str[len(Literal.NIFTY)+1:])

        if cond_str.startswith(Literal.BANKNIFTY):
            self.index, self.operator, self.price = Literal.BANKNIFTY, cond_str[len(
                Literal.BANKNIFTY):len(Literal.BANKNIFTY)+1], float(cond_str[len(Literal.BANKNIFTY)+1:])

    def __repr__(self) -> str:
        return str(self.index + self.operator + str(self.price))
