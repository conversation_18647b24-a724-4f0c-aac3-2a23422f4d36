from email import message
from inspect import trace
import telegram
# from telegram_forwarder import RAKESH_TRADING_GROUP_ID
import utils
from telegram_utils import TelegramUtil
from telethon import events
# from telethon import 
import asyncio
import traceback
import calendar
from time import strptime
from datetime import datetime
from broker import Kotak
from broker import KotakAccount
config = utils.get_config()
import signal
import sys
signal.signal(signal.SIGINT, lambda x, y: sys.exit(0))

# class ProfessorInterpreter:
#     def __init__(self, config) -> None:
#         self.config = config
#         OCTOBER_BATCH_ID = **********

#         #this is sept batch
#         SEPTEMBER_BATCH_ID = **********
#         PROFESSOR_DEBUG_CHANNEL_ID = **********
#         RAKESH_TRADING_GROUP_ID = *********
#         PROFESSOR_PREMIUM_CHANNEL_ID = OCTOBER_BATCH_ID

#         self.telegram_client = TelegramUtil(config).client
#         dialogs = telegram_client.get_dialogs()
#         for d in dialogs:
#             if d.title == 'Professor Debug':
#                 PROFESSOR_TEST_CHANNEL_ID = d.input_entity.channel_id
#             elif d.title == 'October Batch':
#                 OCTOBER_BATCH_ID = d.input_entity.channel_id
#                 PROFESSOR_PREMIUM_CHANNEL_ID = OCTOBER_BATCH_ID
#             elif d.title == 'Rakesh Trading Group':
#                 RAKESH_TRADING_GROUP_ID = d.input_entity.chat_id
#         # BASE_CHANNEL_ID = OCTOBER_BATCH_ID
#         BASE_CHANNEL_IDS = [PROFESSOR_PREMIUM_CHANNEL_ID, PROFESSOR_DEBUG_CHANNEL_ID]


#     @self.telegram_client.on(events.NewMessage(chats=BASE_CHANNEL_IDS))
#     async def my_event_handler(self, event):
#         try:
#             await msg_handler(telegram_client, event)
#             if event.chat.id == PROFESSOR_PREMIUM_CHANNEL_ID:
#                 await telegram_fwd_event_handler(event, forward_chat_id=RAKESH_TRADING_GROUP_ID)
#         except Exception as e:
#             traceback.print_exc()


# OCTOBER_BATCH_ID = **********

#this is sept batch
# SEPTEMBER_BATCH_ID = **********
PROFESSOR_DEBUG_CHANNEL_ID = **********
RAKESH_TRADING_GROUP_ID = *********
NOV_DEC_BATCH = **********
PROFESSOR_PREMIUM_CHANNEL_ID = NOV_DEC_BATCH


# print(PROFESSOR_DEBUG_CHANNEL_ID, PROFESSOR_PREMIUM_CHANNEL_ID, RAKESH_TRADING_GROUP_ID)

INDEX_INSTRUMENTS = ['nifty', 'banknifty', 'finnifty']
CALL_PUT_KEYWORDS = ['ce', 'pe', 'call', 'put']

kotak_account = KotakAccount(config.kotak_userid, config.kotak_pass, config.kotak_access_token, config.kotak_consumer_key, config.kotak_consumer_secret)
kotak = Kotak(kotak_account=kotak_account, parent=True)
# kotak_child_accounts = [Kotak(c) for c in config.kotak_child_account_configs]

def isfloat(num):
    return '.' in num and num.replace('.', '', 1).isdigit()

def is_num(num):
    if num.isdigit():
        return True
    return isfloat(num)


def get_num_lots(msgs):
    n = len(msgs)
    for i in range(n):
        text = msgs[i]
        # If found call put msg, need not look further up, as lot size will never be before call put msg
        if any(t in text for t in CALL_PUT_KEYWORDS) and 'lot' not in text:
            return None
        if not "lot" in text:
            continue
        text = text.split(" ")
        # if msg does not have lot keyword, keep looking further up
        if "lot" in text:
            lot_size = text[text.index('lot')-1]
            if lot_size.isdigit():
                return int(lot_size)
        else:
            # for typos where lot keyword is there but without space from lot size, eg: 2lot
            s = [t for t in text if 'lot' in t][0]
            if s.replace('lot', '').isdigit():
                return int(s.replace('lot', ''))
            else:
                return None
    return None

def get_price(msgs):
    n = len(msgs)
    for i in range(n):
        text = msgs[i]
        text = text.split(" ")
        # if 'sbin' in text:
        #     import pdb; pdb.set_trace()
        if any(t in text for t in CALL_PUT_KEYWORDS):
            if 'at' in text and text.index('at')!=len(text)-1:
                price = text[text.index('at')+1]
                if is_num(price):
                    return float(price)
            else:
                # try to first return any float value in the string. that's most likely price
                for t in text:
                    if isfloat(t):
                        return float(t)

                ce_pe_idx = [text.index(x) for x in CALL_PUT_KEYWORDS if x in text]

                if len(ce_pe_idx) != 1:
                    return None
                ce_pe_idx = ce_pe_idx[0]
                if ce_pe_idx<len(text)-1 and is_num(text[ce_pe_idx+1]):
                    if ce_pe_idx+2<=len(text)-1 and text[ce_pe_idx+2] == 'lot':
                        price = None
                    else:
                        price = float(text[ce_pe_idx+1])
                        return price
            return None
        elif 'at' in text and text.index('at')!=len(text)-1:
            price = text[text.index('at')+1]
            if is_num(price):
                return float(price)
        # Already seen buy in latest msg, and previous msg has price
        elif i == 1 and len(text)==1 and is_num(text[0]) and 'buy' in msgs[0]:
            price = float(text[0])
            return price
    return None

def get_default_num_lots(lot_size, price):
    num_lots = int(int(config.capital/price)/lot_size)
    if num_lots ==0:
        num_lots = 1
    return num_lots

def get_market_price(instrument_id):
    return kotak.get_quote(instrument_id)
    capital_per_trade = config.capital


def get_ce_pe(msgs):
    n = len(msgs)
    for i in range(n):
        text = msgs[i]
        text = text.split(" ")
        if not any(c in text for c in CALL_PUT_KEYWORDS):
            continue
        ce_pe_idx = [text.index(x) for x in CALL_PUT_KEYWORDS if x in text]
        if len(ce_pe_idx) != 1:
            return None
        ce_pe_idx = ce_pe_idx[0]
        return text[ce_pe_idx]

def get_strike(msgs):
    n = len(msgs)
    for i in range(n):
        text = msgs[i]
        # keep looking till you find call put msg as strike will be in the same msg
        text = text.split(" ")
        if not any(c in text for c in CALL_PUT_KEYWORDS):
            continue
        ce_pe_idx = [text.index(x) for x in CALL_PUT_KEYWORDS if x in text]
        if len(ce_pe_idx) != 1:
            return None
        ce_pe_idx = ce_pe_idx[0]
        if ce_pe_idx==0:
            return None
        else:
            probable_strike = text[ce_pe_idx-1]
            if probable_strike.isdigit():
                return int(probable_strike)
            else:
                return None
    return None

def clean_probable_instruments(probable_instruments):
    clean_map = {
        'aartind': 'aartiind',
        'aartind': 'aartiind',
        'airtel': 'bhartiartl',
        'axis': 'axisbank',
        'axisbnak': 'axisbank',
        'bajajauto': 'bajaj-auto',
        'bajajfinance': 'bajfinance',
        'bajajfinserv': 'bajajfinsv',
        'bajajfinserve': 'bajajfinsv',
        'bajajfinsrv': 'bajajfinsv',
        'bergerpaint': 'bergepaint',
        'bharatforge': 'bharatforg',
        'bharatiairtel': 'bhartiartl',
        'bhartiairtl': 'bhartiartl',
        'bhartiairtel': 'bhartiartl',
        'bosch': 'boschltd',
        'chambalfert': 'chamblfert',
        'fiinifty': 'finnifty',
        'hdfc': 'hdfcbank',
        'hdfcbnak': 'hdfcbank',
        'icici': 'icicibank',
        'l&t': 'lt',
        'mothersonsumi': 'motherson',
        'mcdowell': 'mcdowell-n',
        'rec': 'recltd',
        'sbi': 'sbin',
        'tataconsumer': 'tataconsum',
        'tatamotor': 'tatamotors',
        'torntpharma': 'torntpharm',
        'torrentpharma': 'torntpharm',
        'torrentpower': 'torntpower',
        'tvs': 'tvsmotor',
        'ultracem': 'ultracemco',
        'ultracement': 'ultracemco',
        'whirlpul': 'whirlpool',
        'whrlpool': 'whirlpool',
        'zydus': 'zyduslife'
    }

    return [clean_map[i] if i in clean_map else i for i in probable_instruments]

def get_closest_instrument(probable_instruments):
    instruments = ['aartiind', 'abb', 'abbotindia', 'abcapital', 'abfrl', 'acc', 'adanient', 'adaniports', 'alkem', 'amarajabat', 'ambujacem', 'aplltd', 'apollohosp', 'apollotyre', 'ashokley', 'asianpaint', 'astral', 'atul', 'aubank', 'auropharma', 'axisbank', 'bajaj-auto', 'bajajfinsv', 'bajfinance', 'balkrisind', 'balramchin', 'bandhanbnk', 'bankbaroda', 'banknifty', 'bataindia', 'bel', 'bergepaint', 'bharatforg', 'bhartiartl', 'bhel', 'biocon', 'boschltd', 'bpcl', 'britannia', 'bsoft', 'canbk', 'canfinhome', 'chamblfert', 'cholafin', 'cipla', 'coalindia', 'coforge', 'colpal', 'concor', 'coromandel', 'crompton', 'cub', 'cumminsind', 'dabur', 'dalbharat', 'deepakntr', 'deltacorp', 'divislab', 'dixon', 'dlf', 'drreddy', 'eichermot', 'escorts', 'exideind', 'federalbnk', 'finnifty', 'fsl', 'gail', 'glenmark', 'gmrinfra', 'gnfc', 'godrejcp', 'godrejprop', 'granules', 'grasim', 'gspl', 'gujgasltd', 'hal', 'havells', 'hcltech', 'hdfc', 'hdfcamc', 'hdfcbank', 'hdfclife', 'heromotoco', 'hindalco', 'hindcopper', 'hindpetro', 'hindunilvr', 'honaut', 'ibulhsgfin', 'icicibank', 'icicigi', 'icicipruli', 'idea', 'idfc', 'idfcfirstb', 'iex', 'igl', 'indhotel', 'indiacem', 'indiamart', 'indigo', 'indusindbk', 'industower', 'infy', 'intellect', 'ioc', 'ipcalab', 'irctc', 'itc', 'jindalstel', 'jkcement', 'jswsteel', 'jublfood', 'kotakbank', 'l&tfh', 'lalpathlab', 'lauruslabs', 'lichsgfin', 'lt', 'lti', 'ltts', 'lupin', 'm&m', 'm&mfin', 'manappuram', 'marico', 'maruti', 'mcdowell-n', 'mcx', 'metropolis', 'mfsl', 'mgl', 'midcpnifty', 'mindtree', 'motherson', 'mphasis', 'mrf', 'muthootfin', 'nam-india', 'nationalum', 'naukri', 'navinfluor', 'nbcc', 'nestleind', 'nifty', 'nmdc', 'ntpc', 'oberoirlty', 'ofss', 'ongc', 'pageind', 'pel', 'persistent', 'petronet', 'pfc', 'pidilitind', 'piind', 'pnb', 'polycab', 'powergrid', 'pvr', 'rain', 'ramcocem', 'rblbank', 'recltd', 'reliance', 'sail', 'sbicard', 'sbilife', 'sbin', 'shreecem', 'siemens', 'srf', 'srtransfin', 'star', 'sunpharma', 'suntv', 'syngene', 'tatachem', 'tatacomm', 'tataconsum', 'tatamotors', 'tatapower', 'tatasteel', 'tcs', 'techm', 'titan', 'torntpharm', 'torntpower', 'trent', 'tvsmotor', 'ubl', 'ultracemco', 'upl', 'vedl', 'voltas', 'whirlpool', 'wipro', 'zeel', 'zyduslife']
    cleaned_probable_instruments = clean_probable_instruments(probable_instruments)
    closest_instruments = [i for i in instruments if i in cleaned_probable_instruments]
    return closest_instruments[0] if closest_instruments else None


def get_instrument(msgs):
    n = len(msgs)
    for i in range(n):
        text = msgs[i]
        # keep looking till you find call put msg as instrument will be in the same msg
        text = text.split(" ")
        if not any(c in text for c in CALL_PUT_KEYWORDS):
            continue
        ce_pe_idx = [text.index(x) for x in CALL_PUT_KEYWORDS if x in text]
        if len(ce_pe_idx) != 1:
            return None
        ce_pe_idx = ce_pe_idx[0]
        if ce_pe_idx==0:
            return None
        # elif ce_pe_idx == 1 or (ce_pe_idx == 2 and text[0]=='buy'):
        probable_instruments = []
        if ce_pe_idx >=3:
            probable_instruments = [text[ce_pe_idx-2], text[ce_pe_idx-3]+text[ce_pe_idx-2]]
        elif ce_pe_idx >=2:
            probable_instruments = [text[ce_pe_idx-2]]
        if probable_instruments:
            instrument = get_closest_instrument(probable_instruments)
            if instrument:
                return instrument
        probable_strike = text[ce_pe_idx-1]
        if probable_strike.isdigit():
            strike = int(probable_strike)
            if strike>32000 and strike<50000:
                return 'banknifty'
            elif strike>14000 and strike<21000:
                return 'nifty'
            else:
                return None
    return None

def get_expiry(msgs):
    months = [calendar.month_abbr[i].lower() for i in range(1,13)] + [calendar.month_name[i].lower() for i in range(1,13)]
    n = len(msgs)
    expiry_date = expiry_month = None
    for i in range(n):
        text = msgs[i]
        # keep looking till you find call put msg as instrument will be in the same msg
        text = text.split(" ")
        if not any(c in text for c in CALL_PUT_KEYWORDS):
            continue
        if any(m in text for m in ['next week', 'nextweek']):
            return 'next_week', None
        if any(m in text for m in ['monthly']):
            return None, datetime.today().strftime('%b').lower()
        expiry_month = [m for m in months if m in text]
        if not len(expiry_month) == 1:
            expiry_month = expiry_date = None
        else:
            expiry_month = expiry_month[0][:3]
            expiry_month_idx = text.index(expiry_month)
            if expiry_month_idx!=0:
                probable_date = text[expiry_month_idx-1]
                if probable_date.isdigit():
                    expiry_date = int(probable_date)
                elif probable_date.endswith(('st', 'nd', 'rd', 'th')) and probable_date[:2].isdigit():
                    expiry_date = int(probable_date[:2])
        return expiry_date, expiry_month

def process_buy_request(msgs):
        print("process_buy_request_msg: ", msgs)
        msgs = [" ".join(m.split()) for m in msgs]
        num_lots = strike = instrument = price = expiry_date = expiry_month = ce_pe = None
        num_lots = get_num_lots(msgs)
        strike = get_strike(msgs)

        instrument = get_instrument(msgs)
        if instrument:
            # instrument = int(instrument)
            ce_pe = get_ce_pe(msgs).replace('call', 'ce').replace('put', 'pe')
            price = get_price(msgs)
            expiry_date, expiry_month = get_expiry(msgs)
            if expiry_month:
                # expiry_month = int(datetime.today().strftime('%y'))
                expiry_month = strptime(expiry_month,'%b').tm_mon

        return num_lots, instrument.upper() if instrument else None, strike, expiry_date, expiry_month, ce_pe.upper() if ce_pe else None, price


async def msg_handler(telegram_client, event):
    # msessage = event.message
    # print(event.message.id)
    # print(event.message.__dict__)
    # import pdb; pdb.set_trace()
    if event.message and event.message.message:
        text = event.message.message.lower()
        print("incoming_text: ", text)
    else:
        return

    if len(text)>100:
        return

    if 'buy' not in text:
        return
    if any(t in text.split(' ') for t in ['dip', 'will', 'wll', 'strong', 'something', 'buying', 'not', 'was', 'able', 'want', 'also']):
        return

    last_two_msgs = [m.text.lower() async for m in telegram_client.iter_messages(event.chat.id, limit=2, offset_id=event.message.id-3, reverse=True)][::-1]
    msgs = [text]+last_two_msgs
    try:
        num_lots, instrument, strike, expiry_date, expiry_month, ce_pe, price = process_buy_request(msgs)
        print("buy", str(num_lots) + " lot" if num_lots else "", instrument, strike, expiry_date if expiry_date else "", expiry_month if expiry_month else "", ce_pe, "at "+str(price) if price else "")
        instrument_id =  kotak.get_nfo_instrument_id(instrument, strike, ce_pe, expiry_date, expiry_month)
        if instrument_id:
            lot_size = kotak.get_lot_size(instrument_id)
            if not price or (instrument.lower() not in INDEX_INSTRUMENTS):
                price = get_market_price(instrument_id)

            if not num_lots:
                num_lots = get_default_num_lots(lot_size, price)
            quantity = num_lots*lot_size
            kotak.place_order(tradingsymbol = instrument_id, price=price, quantity=quantity)

    except Exception as e:
        trace_msg = traceback.print_exc()
        await telegram_client.send_message(PROFESSOR_DEBUG_CHANNEL_ID, str(msgs)+"\n\n "+str(e.body))


async def telegram_fwd_event_handler(event, telegram_client, forward_chat_id):
    try:
        # print(event.__dict__)
        msg = event.message.message
        # print(event.message.__dict__)
        if event.message.reply_to:
            # print(event.message.reply_to.__dict__)
            reply_to_msg_id = event.message.reply_to_msg_id
            # print(reply_to_msg_id)
            reply_msg = await telegram_client.get_messages(event.chat.id, ids=[reply_to_msg_id])
            reply_msg = reply_msg[0].message
            print(reply_msg)
            # msg_id_prefix = "MsgId: "+ str(reply_to_msg_id)
            async for message in telegram_client.iter_messages(forward_chat_id, limit=100):
                # print(message.id, message.text)
                if message and message.text == reply_msg:
                    await telegram_client.send_message(forward_chat_id, msg, reply_to=message.id)
                    break
        else:
            await telegram_client.send_message(forward_chat_id, msg)
    except Exception as e:
        traceback.print_exc()


async def professor_event_handler(event, telegram_client):
    try:
        await msg_handler(telegram_client, event)
        if event.chat.id == PROFESSOR_PREMIUM_CHANNEL_ID:
            await telegram_fwd_event_handler(event, telegram_client=telegram_client, forward_chat_id=RAKESH_TRADING_GROUP_ID)
    except Exception as e:
        traceback.print_exc()

def main():

    # await telegram_client.send_message(PROFESSOR_TEST_CHANNEL_ID, 'sample message')
    # lot_size, instrument, strike, expiry_date, expiry_month, ce_pe = process_buy_request(['so those are high risk could buy  17500 call monthly at', 'but if gap down these stock option will not fall much', 'gap possible'])
    # import pdb; pdb.set_trace()
    # print(kotak.get_nfo_instrument_id(instrument, expiry_date, expiry_month, strike, ce_pe))
    # await forward_all_msgs(telegram_client)
    # telegram_client.send_message(professor_test_channel_entity, 'hello world')
    # telegram_client.send_message(PROFESSOR_TEST_CHANNEL_ID, 'hello world')


    # for msg in telegram_client.iter_messages(SEPTEMBER_BATCH_ID, limit=1000, reverse=True):
    #     if not msg or not msg.text:
    #         continue
    #     text = msg.text.lower()
    #     if len(text)>100:
    #         continue
    #     if 'buy' not in text:
    #         continue
    #     if any(t in text for t in ['dip', 'will', 'wll', 'strong', 'something', 'buying', 'not', 'was', 'able', 'want', 'also']):
    #         continue
    #     last_two_msgs = [m.text.lower() for m in telegram_client.iter_messages(SEPTEMBER_BATCH_ID, limit=2, offset_id=msg.id-3, reverse=True)][::-1]
    #     msgs = [text]+last_two_msgs
    #     lot_size, instrument, strike, expiry_date, expiry_month, ce_pe, price = process_buy_request(msgs)
    #     print(lot_size, " lot ", instrument, strike, ce_pe, " price ", price)
    #     print(kotak.get_nfo_instrument_id(instrument, expiry_date, expiry_month, strike, ce_pe), "\n")

    telegram_client = TelegramUtil(config).client
    dialogs = telegram_client.get_dialogs()
    for d in dialogs:
        if d.title == 'Professor Debug':
            PROFESSOR_DEBUG_CHANNEL_ID = d.input_entity.channel_id
        elif 'NOV-DEC Batch' in d.title:
            NOV_DEC_BATCH = d.input_entity.channel_id
            PROFESSOR_PREMIUM_CHANNEL_ID = NOV_DEC_BATCH
        elif d.title == 'Rakesh Trading Group':
            print(d.__dict__)
            RAKESH_TRADING_GROUP_ID = d.input_entity.chat_id
            print(RAKESH_TRADING_GROUP_ID)
    BASE_CHANNEL_IDS = [PROFESSOR_PREMIUM_CHANNEL_ID, PROFESSOR_DEBUG_CHANNEL_ID]

    # @telegram_client.on(events.NewMessage(chats=BASE_CHANNEL_IDS))
    # async def my_event_handler(event):
    #     await professor_event_handler(event, telegram_client=telegram_client)

    # loop = asyncio.get_event_loop()
    # loop.create_task(telegram_client.run_until_disconnected())
    # loop.run_forever()


if __name__ == "__main__":
    main()

# asyncio.run(main())
