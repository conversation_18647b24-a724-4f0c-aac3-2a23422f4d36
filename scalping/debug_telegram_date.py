from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service
import time
import os
from datetime import datetime, timedel<PERSON>

def debug_telegram_date_detection():
    """Debug script to see what text is being captured from Telegram messages"""
    
    # Setup Chrome options for mobile view
    options = webdriver.ChromeOptions()
    
    # Mobile device emulation (iPhone 13 Pro)
    mobile_emulation = {
        "deviceMetrics": {
            "width": 375,
            "height": 812,
            "pixelRatio": 3.0
        },
        "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"
    }
    options.add_experimental_option("mobileEmulation", mobile_emulation)
    options.add_argument("--window-size=375,812")
    
    user_data_dir = os.path.join(os.path.expanduser("~"), "telegram_selenium_profile")
    options.add_argument(f"--user-data-dir={user_data_dir}")
    
    # Initialize the driver
    try:
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=options)
    except Exception as e:
        print(f"Failed to initialize Chrome driver: {e}")
        driver = webdriver.Chrome(options=options)

    try:
        # Navigate to Telegram channel
        channel_url = "https://web.telegram.org/a/#-1002075758731"
        driver.get(channel_url)
        time.sleep(5)
        
        # Wait for messages to load
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CLASS_NAME, "bubble"))
        )
        
        print("Channel loaded, analyzing messages...")
        
        # Get yesterday's date patterns
        yesterday = datetime.now() - timedelta(days=1)
        target_patterns = [
            yesterday.strftime("%d %B %Y"),  # 13 August 2025
            yesterday.strftime("%-d %B %Y"), # 13 August 2025 (no leading zero)
        ]
        
        print(f"Looking for patterns: {target_patterns}")
        
        # Debug script to examine all message content
        debug_script = f"""
        const targetPatterns = {target_patterns};

        // Try multiple selectors to find actual message bubbles
        const selectors = [
            '.bubble',
            '[class*="bubble"]',
            '.message',
            '[class*="message"]',
            '.Message',
            '[class*="Message"]',
            '.chat-bubble',
            '[class*="chat"]',
            '.text-content',
            '[class*="text"]'
        ];

        let allMessages = [];
        selectors.forEach(selector => {{
            const elements = document.querySelectorAll(selector);
            elements.forEach(el => {{
                if (!allMessages.includes(el) && el.innerText && el.innerText.trim().length > 0) {{
                    allMessages.push(el);
                }}
            }});
        }});

        console.log('Found', allMessages.length, 'potential message elements');

        let results = [];
        
        // Check last 10 messages (or all if fewer than 10)
        const messagesToCheck = allMessages.slice(-10);

        for (let i = 0; i < messagesToCheck.length; i++) {{
            const message = messagesToCheck[i];
            
            // Scroll into view
            message.scrollIntoView({{ block: 'center' }});
            
            // Trigger various hover events
            const rect = message.getBoundingClientRect();
            const centerX = rect.left + rect.width / 2;
            const centerY = rect.top + rect.height / 2;
            
            ['mouseover', 'mouseenter', 'mousemove'].forEach(eventType => {{
                const event = new MouseEvent(eventType, {{
                    view: window,
                    bubbles: true,
                    cancelable: true,
                    clientX: centerX,
                    clientY: centerY
                }});
                message.dispatchEvent(event);
            }});
            
            // Wait a bit for tooltips
            setTimeout(() => {{}}, 100);
            
            // Collect all text from message and its children
            let allText = message.innerText || message.textContent || '';
            let titleText = message.getAttribute('title') || '';
            
            // Check all child elements
            const allElements = message.querySelectorAll('*');
            let childTexts = [];
            let childTitles = [];
            
            allElements.forEach(el => {{
                const text = el.innerText || el.textContent || '';
                const title = el.getAttribute('title') || '';
                if (text.trim()) childTexts.push(text.trim());
                if (title.trim()) childTitles.push(title.trim());
            }});
            
            results.push({{
                index: i,
                mainText: allText.substring(0, 200),
                titleAttr: titleText,
                childTexts: childTexts.slice(0, 5), // First 5 child texts
                childTitles: childTitles.slice(0, 5), // First 5 child titles
                hasTargetPattern: targetPatterns.some(pattern => 
                    (allText + ' ' + titleText + ' ' + childTitles.join(' ')).toLowerCase().includes(pattern.toLowerCase())
                )
            }});
        }}
        
        return results;
        """
        
        # Execute debug script
        results = driver.execute_script(debug_script)
        
        print("\n=== MESSAGE ANALYSIS ===")
        for result in results:
            print(f"\nMessage {result['index']}:")
            print(f"  Main text: {result['mainText']}")
            print(f"  Title attr: {result['titleAttr']}")
            print(f"  Child texts: {result['childTexts']}")
            print(f"  Child titles: {result['childTitles']}")
            print(f"  Has target pattern: {result['hasTargetPattern']}")
            print("-" * 50)
        
        # Try more aggressive hover detection on recent messages
        print("\n=== AGGRESSIVE HOVER TEST ===")
        aggressive_hover_script = """
        // Find actual message bubbles
        const bubbles = document.querySelectorAll('.bubble');
        const lastMessage = bubbles[bubbles.length - 1];
        
        if (lastMessage) {
            lastMessage.scrollIntoView({ block: 'center' });
            
            // Try hovering over every pixel in the message
            const rect = lastMessage.getBoundingClientRect();
            let tooltipTexts = [];
            
            for (let x = rect.left; x < rect.right; x += 10) {
                for (let y = rect.top; y < rect.bottom; y += 10) {
                    const element = document.elementFromPoint(x, y);
                    if (element) {
                        ['mouseover', 'mouseenter', 'mousemove'].forEach(eventType => {
                            const event = new MouseEvent(eventType, {
                                view: window,
                                bubbles: true,
                                cancelable: true,
                                clientX: x,
                                clientY: y
                            });
                            element.dispatchEvent(event);
                        });
                        
                        // Check for any new title attributes
                        const title = element.getAttribute('title');
                        if (title && !tooltipTexts.includes(title)) {
                            tooltipTexts.push(title);
                        }
                    }
                }
            }
            
            return {
                messageText: lastMessage.innerText || lastMessage.textContent || '',
                tooltipTexts: tooltipTexts
            };
        }
        
        return null;
        """
        
        hover_result = driver.execute_script(aggressive_hover_script)
        if hover_result:
            print(f"Last message text: {hover_result['messageText']}")
            print(f"Found tooltips: {hover_result['tooltipTexts']}")
        
    finally:
        input("Press Enter to close browser...")
        driver.quit()

if __name__ == "__main__":
    debug_telegram_date_detection()
