from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
import time
import os
from datetime import datetime, timedelta

def debug_hover_tooltips():
    """Debug script to specifically test hover tooltips on Telegram messages"""
    
    # Setup Chrome options
    options = webdriver.ChromeOptions()
    mobile_emulation = {
        "deviceMetrics": {"width": 375, "height": 812, "pixelRatio": 3.0},
        "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"
    }
    options.add_experimental_option("mobileEmulation", mobile_emulation)
    options.add_argument("--window-size=375,812")
    
    user_data_dir = os.path.join(os.path.expanduser("~"), "telegram_selenium_profile")
    options.add_argument(f"--user-data-dir={user_data_dir}")
    
    driver = webdriver.Chrome(options=options)
    actions = ActionChains(driver)

    try:
        # Navigate to Telegram channel
        driver.get("https://web.telegram.org/a/#-1002075758731")
        time.sleep(5)
        
        # Wait for messages to load
        WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.CLASS_NAME, "bubble")))
        
        print("Testing hover tooltips on message elements...")
        
        # Target date pattern
        yesterday = datetime.now() - timedelta(days=1)
        target_date = yesterday.strftime("13 August 2025")
        
        # Script to find and test hover on all elements
        hover_test_script = f"""
        const targetDate = '{target_date}';
        
        // Find all bubble elements (actual messages)
        const bubbles = document.querySelectorAll('.bubble');
        console.log('Found', bubbles.length, 'message bubbles');
        
        let results = [];
        
        // Test last 5 bubbles
        const bubblesToTest = Array.from(bubbles).slice(-5);
        
        bubblesToTest.forEach((bubble, index) => {{
            bubble.scrollIntoView({{ block: 'center' }});
            
            // Find all child elements that might have hover tooltips
            const allElements = bubble.querySelectorAll('*');
            const elementsToHover = [bubble, ...allElements];
            
            let bubbleResult = {{
                bubbleIndex: bubbles.length - 5 + index,
                mainText: bubble.innerText || bubble.textContent || '',
                hoveredElements: []
            }};
            
            elementsToHover.forEach((element, elemIndex) => {{
                // Get initial state
                const initialTitle = element.getAttribute('title') || '';
                
                // Trigger hover events
                const rect = element.getBoundingClientRect();
                if (rect.width > 0 && rect.height > 0) {{
                    const centerX = rect.left + rect.width / 2;
                    const centerY = rect.top + rect.height / 2;
                    
                    ['mouseenter', 'mouseover', 'mousemove'].forEach(eventType => {{
                        const event = new MouseEvent(eventType, {{
                            view: window,
                            bubbles: true,
                            cancelable: true,
                            clientX: centerX,
                            clientY: centerY
                        }});
                        element.dispatchEvent(event);
                    }});
                    
                    // Check for title after hover
                    setTimeout(() => {{
                        const afterTitle = element.getAttribute('title') || '';
                        const elementText = element.innerText || element.textContent || '';
                        
                        if (afterTitle || elementText.includes(':') || elementText.includes('AM') || elementText.includes('PM')) {{
                            bubbleResult.hoveredElements.push({{
                                elementIndex: elemIndex,
                                tagName: element.tagName,
                                className: element.className,
                                initialTitle: initialTitle,
                                afterTitle: afterTitle,
                                elementText: elementText.substring(0, 100),
                                hasTargetDate: (afterTitle + elementText).includes(targetDate)
                            }});
                        }}
                    }}, 50);
                }}
            }});
            
            results.push(bubbleResult);
        }});
        
        // Wait for all hover effects to complete
        setTimeout(() => {{
            return results;
        }}, 1000);
        
        return results;
        """
        
        # Execute the hover test
        print("Executing hover test...")
        results = driver.execute_script(hover_test_script)
        time.sleep(2)  # Wait for hover effects
        
        # Get updated results
        get_results_script = """
        const bubbles = document.querySelectorAll('.bubble');
        const bubblesToTest = Array.from(bubbles).slice(-5);
        
        return bubblesToTest.map((bubble, index) => {
            const allElements = bubble.querySelectorAll('*');
            
            let hoveredElements = [];
            [bubble, ...allElements].forEach((element, elemIndex) => {
                const title = element.getAttribute('title') || '';
                const text = element.innerText || element.textContent || '';
                
                if (title || text.includes(':') || text.includes('AM') || text.includes('PM')) {
                    hoveredElements.push({
                        elementIndex: elemIndex,
                        tagName: element.tagName,
                        className: element.className,
                        title: title,
                        text: text.substring(0, 100),
                        hasTargetDate: (title + text).toLowerCase().includes('13 august 2025')
                    });
                }
            });
            
            return {
                bubbleIndex: bubbles.length - 5 + index,
                mainText: bubble.innerText || bubble.textContent || '',
                hoveredElements: hoveredElements
            };
        });
        """
        
        final_results = driver.execute_script(get_results_script)
        
        print("\n=== HOVER TEST RESULTS ===")
        for result in final_results:
            print(f"\nBubble {result['bubbleIndex']}:")
            print(f"  Main text: {result['mainText']}")
            print(f"  Hovered elements found: {len(result['hoveredElements'])}")
            
            for elem in result['hoveredElements']:
                print(f"    Element {elem['elementIndex']} ({elem['tagName']}.{elem['className']}):")
                print(f"      Title: {elem['title']}")
                print(f"      Text: {elem['text']}")
                print(f"      Has target date: {elem['hasTargetDate']}")
            print("-" * 50)
        
        # Try ActionChains approach for more realistic hover
        print("\n=== TESTING ACTIONCHAINS HOVER ===")
        bubbles = driver.find_elements(By.CLASS_NAME, "bubble")
        if bubbles:
            last_bubble = bubbles[-1]
            print(f"Hovering over last bubble with ActionChains...")
            
            # Scroll to bubble
            driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", last_bubble)
            time.sleep(1)
            
            # Hover using ActionChains
            actions.move_to_element(last_bubble).perform()
            time.sleep(2)
            
            # Check for any new tooltips
            tooltip_check = """
            const bubble = arguments[0];
            const allElements = bubble.querySelectorAll('*');
            
            let tooltips = [];
            [bubble, ...allElements].forEach(element => {
                const title = element.getAttribute('title') || '';
                if (title) {
                    tooltips.push(title);
                }
            });
            
            return {
                bubbleText: bubble.innerText || bubble.textContent || '',
                tooltips: tooltips
            };
            """
            
            tooltip_result = driver.execute_script(tooltip_check, last_bubble)
            print(f"Bubble text: {tooltip_result['bubbleText']}")
            print(f"Found tooltips: {tooltip_result['tooltips']}")
        
    finally:
        input("Press Enter to close browser...")
        driver.quit()

if __name__ == "__main__":
    debug_hover_tooltips()
