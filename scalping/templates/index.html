<!DOCTYPE HTML>
<html>
<head>
    <title>Socket-Test</title>
<!--    <link rel="stylesheet" href="/temlates/css/style.css">-->
    <script src="//code.jquery.com/jquery-1.12.4.min.js"></script>
    <script src="//cdnjs.cloudflare.com/ajax/libs/socket.io/2.2.0/socket.io.js"></script>
    <script type="text/javascript" charset="utf-8">
        $(document).ready(function() {

            namespace = '/test';
            var socket = io(namespace);

            socket.on('connect', function() {
                socket.emit('my_event', {data: 'connected to the SocketServer...'});
            });

            $('form#buy').submit(function(event) {
                socket.emit('my_buy_event', {data: $('#emit_buy').val()});
                return false;
            });

            socket.on('bought', function(msg, cb) {
                $('#log').prepend('<br>' + $('<div/>').text('logs #' + ': ' + msg.data).html());
                // $('#log').prepend($('<div/>').text('logs #' + ':   ' + msg.data + ': ' + msg.idx + ':   ' + msg.diff + ': ' + msg.ltp).html() + '<br>');
                if (cb)
                    cb();
            });


            socket.on('my_response', function(msg, cb) {
                // $('#log').append('<br>' + $('<div/>').text('logs #' + ': ' + msg.data).html());
                // $('#log').prepend($('<div/>').text('logs #' + ':   ' + msg.data + ': ' + msg.idx + ':   ' + msg.diff + ': ' + msg.ltp).html() + '<br>');
                $('#log').prepend('<form id="order" method="POST" action="/order" target="dummyframe"><input type="text" readonly id="order_time" name="order_time" value=' + msg.idx + '>' + '<input type="text" readonly id="diff" name="diff" value=' + msg.diff + '>' + '<input type="text" readonly id="order_price" name="order_price" value=' + msg.ltp + '>' + '<input type="submit" name="buy_button" id="buy_button" value="Buy" />' + '<input type="submit" name="sell_button" id="sell_button" value="Sell" />' +'</form>' + '<br>');
                if (cb)
                    cb();
            });

            $('form#emit').submit(function(event) {
                socket.emit('my_event', {data: $('#emit_data').val()});
                return false;
            });
            $('form#broadcast').submit(function(event) {
                socket.emit('my_broadcast_event', {data: $('#broadcast_data').val()});
                return false;
            });
            $('form#disconnect').submit(function(event) {
                socket.emit('disconnect_request');
                return false;
            });
        });
    </script>
</head>
<style>
    input[type="submit"] {
        border: 1px solid #000;
        padding: 10px;
        width: 110px;
        margin-left: 20px;
        font-size: 18px;
    }
    input {
        border: none;
    }
    input#buy_button {
        background-color: #5b9a5d;
    }
    input#sell_button {
        background-color: #e25f5b;
    }
    form#order {
        border-bottom: 1px solid #dcdfe6;
        padding-bottom: 20px;
    }
    form#order:hover {
        background-color: rgba(0, 0, 0, .075);
    }
</style>

<body style="background-color:white;">
<iframe name="dummyframe" id="dummyframe" style="display: none;"></iframe>

<h1 style="background-color:white;">Socket</h1>
<form id="emit" method="POST" action='#'>
    <input type="text" name="emit_data" id="emit_data" placeholder="Message">
    <input type="submit" value="Send Message">
</form>
<form id="broadcast" method="POST" action='#'>
    <input type="text" name="broadcast_data" id="broadcast_data" placeholder="Message">
    <input type="submit" value="Send Broadcast Message">
</form>

<form id="disconnect" method="POST" action="#">
    <input type="submit" value="Disconnect Server">
</form>
<form id="buy" method="POST" action="#"><input type="submit" name="emit_buy" id="emit_buy" value=' + msg.idx + '></form>
<h2 style="background-color:white;">Logs</h2>
<div id="log" ></div>
</body>
</html>
