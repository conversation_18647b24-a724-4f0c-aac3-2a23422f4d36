class TradeObserver:
    def __init__(self, algo, fno_lot_data):
        self.algo = algo
        self.fno_lot_data = fno_lot_data

    def update(self, script, tradingsymbol, price, target=None, sl=None):
        script_lot_data = self.fno_lot_data[script]
        script_lot_size = script_lot_data['lot_size']
        live_balance = self.algo.broker.get_margin()['available']['live_balance']
        # live_balance = 20000
        capital_per_trade = min(self.algo.config.capital, live_balance)
        script_freeze_limit = script_lot_data['freeze_limit']
        # import pdb; pdb.set_trace()
        qty = min(int((capital_per_trade/price)/script_lot_size)*script_lot_size, script_freeze_limit)
        if qty == 0:
            print("Not enough balance")
            return
        print("placing_order_for: "+tradingsymbol+' @'+str(price)+ ' for size '+str(qty))
        self.algo.broker.place_order(tradingsymbol=tradingsymbol, transaction_type='BUY', quantity=qty,
            product='NRML', order_type='LIMIT', price=price)
        if sl and target:
            print('placing_gtt')
            self.algo.broker.place_sl_target_gtt(tradingsymbol=tradingsymbol, quantity=qty, curr_price=price, sl_price=sl, target_price=target)

    def update2(self, script, strike_price, ce_pe, price, num_lots=1, target=None, sl=None):
        instrument_id = self.algo.broker2.get_nfo_instrument_id(instrument_name=script, strike_price=strike_price, ce_pe=ce_pe, expiry_date=None, expiry_month=None)
        lot_size = self.algo.broker2.get_lot_size(instrument_id)
        quantity = num_lots*lot_size
        try:
            self.algo.broker2.place_order(tradingsymbol = instrument_id, price=price, quantity=quantity)
        except: 
            pass