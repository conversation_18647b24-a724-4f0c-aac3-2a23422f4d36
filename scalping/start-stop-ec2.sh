#!/bin/bash

# Replace the following values with your own
# rakesh-ec2 instance id
INSTANCE_ID="i-0b15dafbc6511ad6c"
REGION="ap-south-1"
USERNAME="ec2-user"
KEY_FILE="/Users/<USER>/.ssh/id_rsa"
SCRIPT_DIR="/home/<USER>/scalping"
SCRIPT_FILE="main.py"
TMUX_SESSION_NAME="rakesh"

# Check if the AWS CLI is installed
if ! [ -x "$(command -v aws)" ]; then
  echo 'Error: AWS CLI is not installed.' >&2
  exit 1
fi

# Check if the action is specified
if [ -z "$1" ]; then
  echo "Error: Action is not specified." >&2
  exit 1
fi
# Get the action (start or stop)
action=$1

# Check if the action is valid
if [ "$action" != "start" ] && [ "$action" != "stop" ]; then
  echo "Error: Invalid action. Valid actions are start and stop." >&2
  exit 1
fi

if [ "$action" == "start" ]; then
  nohup aws ec2 start-instances --instance-ids $INSTANCE_ID --region $REGION &> /dev/null &
  # Wait for the instance to start
  sleep 60
  # Get the public IP address of the instance
  IP_ADDRESS=$(aws ec2 describe-instances --instance-ids $INSTANCE_ID --region $REGION --query 'Reservations[0].Instances[0].PublicIpAddress' --output text)

  # Connect to the instance using SSH and start a tmux session
  ssh -t -i $KEY_FILE $USERNAME@$IP_ADDRESS "tmux new-session -d -s $TMUX_SESSION_NAME"

  # Run the Python script inside the tmux session
  ssh -t -i $KEY_FILE $USERNAME@$IP_ADDRESS "tmux send-keys -t $TMUX_SESSION_NAME 'cd $SCRIPT_DIR && python $SCRIPT_FILE' C-m"
elif [ "$action" == "stop" ]; then
  # Stop the EC2 instance
  nohup aws ec2 stop-instances --instance-ids $INSTANCE_ID --region $REGION &> /dev/null &
fi
