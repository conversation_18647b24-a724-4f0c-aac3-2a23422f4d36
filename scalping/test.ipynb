{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                          time                 symbol   price  sell_price  \\\n", "0    2024-01-01 09:16:00+05:30          IDEA24JAN15PE    0.80        0.70   \n", "1    2024-01-01 09:31:00+05:30     ASHOKLEY24JAN180PE    5.10        4.25   \n", "2    2024-01-01 10:30:00+05:30        BSOFT24JAN700PE   29.15         NaN   \n", "3    2024-01-01 12:07:00+05:30  BHARTIARTL24JAN1000PE   14.90         NaN   \n", "4    2024-01-01 15:24:00+05:30       DIXON24JAN6300PE  193.70         NaN   \n", "..                         ...                    ...     ...         ...   \n", "216  2024-02-26 09:50:00+05:30     INDHOTEL24FEB575PE    5.55        6.75   \n", "217  2024-02-26 09:50:00+05:30      VOLTAS24FEB1070PE    8.00       10.10   \n", "218  2024-02-26 10:50:00+05:30         ABB24FEB5100PE   53.05       61.65   \n", "219  2024-02-26 12:08:00+05:30       ALKEM24FEB5100PE   80.00       95.00   \n", "220  2024-02-26 12:11:00+05:30    HINDPETRO24FEB505PE    6.40        4.35   \n", "\n", "                     sell_time  \\\n", "0    2024-01-01 09:35:00+05:30   \n", "1    2024-01-01 09:41:00+05:30   \n", "2                          NaN   \n", "3                          NaN   \n", "4                          NaN   \n", "..                         ...   \n", "216  2024-02-26 09:52:00+05:30   \n", "217  2024-02-26 09:51:00+05:30   \n", "218  2024-02-26 10:53:00+05:30   \n", "219  2024-02-26 12:10:00+05:30   \n", "220  2024-02-26 12:29:00+05:30   \n", "\n", "                                            strategy  success_multiplier  \n", "0    candle_delta_gt_20000_and_price_incr_10_perc_pe                 1.2  \n", "1    candle_delta_gt_20000_and_price_incr_10_perc_pe                 1.2  \n", "2    candle_delta_gt_20000_and_price_incr_10_perc_pe                 1.2  \n", "3    candle_delta_gt_20000_and_price_incr_10_perc_pe                 1.2  \n", "4    candle_delta_gt_20000_and_price_incr_10_perc_pe                 1.2  \n", "..                                               ...                 ...  \n", "216   candle_delta_gt_50000_and_price_incr_5_perc_pe                 1.1  \n", "217   candle_delta_gt_50000_and_price_incr_5_perc_pe                 1.1  \n", "218   candle_delta_gt_50000_and_price_incr_5_perc_pe                 1.1  \n", "219   candle_delta_gt_50000_and_price_incr_5_perc_pe                 1.1  \n", "220   candle_delta_gt_50000_and_price_incr_5_perc_pe                 1.1  \n", "\n", "[9478 rows x 7 columns]\n"]}], "source": ["from telegram_utils import TelegramUtil\n", "from broker import Zerodha\n", "import time\n", "import psycopg2\n", "import psycopg2.pool\n", "import logging\n", "from multiprocessing import Process\n", "import urllib.request\n", "from broker_singleton import BrokerSingleton\n", "from unusual_option_activity import UnusualOptionActivity\n", "from filtered_instruments import FilteredInstruments\n", "import calendar\n", "import pandas as pd\n", "import utils\n", "import glob\n", "pd.options.mode.copy_on_write = True\n", "pd.set_option('display.max_columns', None)  # or 1000\n", "pd.set_option('display.max_rows', None)  # or 1000\n", "pd.set_option('display.max_colwidth', None)  # or 199\n", "\n", "config = utils.get_config()\n", "broker = BrokerS<PERSON>leton()\n", "# print(config.zerodha_username, config.zerodha_enctoken, config.zerodha_public_token)\n", "# q = broker.get_quote('NFO:'+'ASIANPAINT24MAR3000CE')['NFO:'+'ASIANPAINT24MAR3000CE']\n", "\n", "# print(q)\n", "\n", "\n", "# Append all CSV files into a single dataframe in the directory strategy/*.csv\n", "# df = pd.concat([pd.read_csv(file) for file in glob.glob('strategy/*.csv')])\n", "df = pd.concat([pd.read_csv(file) for file in glob.glob('strategy/*.csv')])\n", "df[['date', 'timestamp']] = df['time'].str.split(expand=True)\n", "\n", "df['date'] = pd.to_datetime(df['date'])\n", "\n", "df['timestamp'] = pd.to_datetime(df['timestamp']).dt.time\n", "df['pnl'] = (df['sell_price'] - df['price']) / df['price'] * 100\n", "\n", "df['hour'] = pd.to_datetime(df['timestamp'].astype(str)).dt.hour\n", "\n", "df['day'] = df['date'].dt.day\n", "\n", "\n", "df = df[['date', 'timestamp', 'symbol', 'price', 'sell_price', 'pnl', 'strategy', 'success_multiplier', 'hour']]\n", "\n", "df.groupby(['strategy', 'success_multiplier'])['pnl'].mean()\n", "df[(df['strategy'] == 'candle_delta_gt_10000_and_price_incr_10_perc_pe') & (df['timestamp']<='10:30:00')].groupby('date')['pnl'].sum()\n", "# Group by date and find the sum of pnl\n", "# df_grouped = df.groupby('date')['pnl'].sum()\n", "\n", "# df['timestamp'] = pd.to_datetime(df['timestamp'])\n", "\n", "df['hour'] = pd.to_datetime(df['timestamp'].astype(str)).dt.hour\n", "\n", "df.groupby('hour').sum()\n", "\n", "\n", "df[(df['strategy'] == 'candle_delta_gt_10000_and_price_incr_10_perc_pe')].groupby('date')['pnl'].sum()\n", "\n", "print(df)\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["db_conn = psycopg2.connect(\n", "    host=config.db_host,\n", "    database=config.db_name,\n", "    user=config.db_user,\n", "    port=config.db_port\n", ")\n", "\n", "def get_trading_days(year, month, date_starting_from=None):\n", "    # Get all the dates in a given month and year\n", "    def get_last_thursday(year, month):\n", "        cal = calendar.monthcalendar(year, month)\n", "        last_week = cal[-1]\n", "\n", "        if last_week[calendar.THURSDAY] != 0:\n", "            last_thursday = last_week[calendar.THURSDAY]\n", "        else:\n", "            last_thursday = cal[-2][calendar.THURSDAY]\n", "\n", "        return last_thursday\n", "\n", "    dates = []\n", "    num_days = calendar.monthrange(year, month)[1]\n", "    for day in range(1, num_days + 1):\n", "        date = datetime.date(year, month, day)\n", "        dates.append(date)\n", "    holidays = ['2023-01-26', '2023-03-07', '2023-03-30', '2023-04-04', '2023-04-07', '2023-04-14', '2023-05-01', '2023-06-29', '2023-08-15', '2023-09-19', '2023-10-02', '2023-10-24', '2023-11-14', '2023-11-27', '2023-12-25', '2024-01-22', '2024-01-26', '2024-03-08', '2024-03-25', '2024-03-29', '2024-04-11', '2024-04-17', '2024-05-01', '2024-06-17', '2024-07-17', '2024-08-15', '2024-10-02', '2024-11-01', '2024-11-15', '2024-12-25']\n", "    trading_days = [d for d in dates if d.weekday() < 5 and str(d) not in holidays]\n", "    if date_starting_from:\n", "        trading_days = [t for t in trading_days if t.day>=date_starting_from]\n", "    last_thursday = get_last_thursday(year, month)\n", "    trading_days = [t for t in trading_days if t.day<=last_thursday]\n", "    return trading_days\n", "\n", "\n", "trading_days = get_trading_days(2024, 2)\n", "instrument_list = {}\n", "instruments_query = f\"\"\"\n", "    SELECT symbol, lot_size from instruments\n", "\"\"\"\n", "\n", "with db_conn.cursor() as cur:\n", "    cur.execute(instruments_query)\n", "    rows = cur.fetchall()\n", "    instrument_list = {r[0]: r[1] for r in rows}\n", "df_all_stocks = pd.read_csv('2024/fno_stocks_1min_data/all_stocks.csv')\n", "df_all_stocks.rename(columns={'ticker': 'spot_symbol', 'date': 'time', 'close': 'spot_price'}, inplace=True)\n", "# 2024-01-01 09:15:00+05:30\n", "df_all_stocks['time'] = pd.to_datetime(df_all_stocks['time'], format='%Y-%m-%d %H:%M:%S%z')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                            time               symbol spot_symbol  price  \\\n", "0      2024-01-01 09:15:00+05:30   AARTIIND24JAN600CE    AARTIIND  63.25   \n", "1      2024-01-01 09:15:00+05:30   AARTIIND24JAN600PE    AARTIIND   8.90   \n", "2      2024-01-01 09:15:00+05:30   AARTIIND24JAN650CE    AARTIIND  31.75   \n", "3      2024-01-01 09:15:00+05:30   AARTIIND24JAN680CE    AARTIIND  19.80   \n", "4      2024-01-01 09:15:00+05:30   AARTIIND24JAN710CE    AARTIIND  11.90   \n", "...                          ...                  ...         ...    ...   \n", "650891 2024-01-01 15:29:00+05:30  ZYDUSLIFE24JAN690PE   ZYDUSLIFE  22.00   \n", "650892 2024-01-01 15:29:00+05:30  ZYDUSLIFE24JAN700CE   ZYDUSLIFE  30.85   \n", "650893 2024-01-01 15:29:00+05:30  ZYDUSLIFE24JAN700PE   ZYDUSLIFE  27.70   \n", "650894 2024-01-01 15:29:00+05:30  ZYDUSLIFE24JAN720CE   ZYDUSLIFE  22.00   \n", "650895 2024-01-01 15:29:00+05:30  ZYDUSLIFE24JAN730CE   ZYDUSLIFE  18.40   \n", "\n", "         high    low  volume       oi  spot_price ce_pe  strike_price  \\\n", "0       63.25  63.25    1000   171000      649.65    CE         600.0   \n", "1        8.90   8.90    1000   716000      649.65    PE         600.0   \n", "2       33.00  31.75    4000   675000      649.65    CE         650.0   \n", "3       19.80  19.80    1000   302000      649.65    CE         680.0   \n", "4       11.90  11.90    1000   138000      649.65    CE         710.0   \n", "...       ...    ...     ...      ...         ...   ...           ...   \n", "650891  22.40  22.00     900   129600      697.20    PE         690.0   \n", "650892  30.85  30.60   16200  1730700      697.20    CE         700.0   \n", "650893  27.70  27.70    2700   331200      697.20    PE         700.0   \n", "650894  22.00  22.00    6300   688500      697.20    CE         720.0   \n", "650895  18.40  18.40    1800   438300      697.20    CE         730.0   \n", "\n", "        traded_value  \n", "0            63250.0  \n", "1             8900.0  \n", "2           127000.0  \n", "3            19800.0  \n", "4            11900.0  \n", "...              ...  \n", "650891       19800.0  \n", "650892      499770.0  \n", "650893       74790.0  \n", "650894      138600.0  \n", "650895       33120.0  \n", "\n", "[650896 rows x 12 columns]\n"]}], "source": ["day = 1\n", "month = 1\n", "year = 2024\n", "month_name = calendar.month_abbr[month].upper()\n", "df_file_name = f'/Users/<USER>/Downloads/2024/GFDLNFO_BACKADJUSTED_{day:02d}{month:02d}{year:04d}.csv'\n", "df = pd.read_csv(df_file_name)\n", "df['time'] = pd.to_datetime(df['Date'] + ' ' + df['Time']+'+05:30', format='%d/%m/%Y %H:%M:%S%z')\n", "df['time'] = df['time'] - pd.<PERSON><PERSON><PERSON>(seconds=59)\n", "df.rename(columns={'Ticker': 'symbol', 'Close': 'price', 'High': 'high', 'Low': 'low', 'Volume': 'volume', 'Open Interest': 'oi'}, inplace=True)\n", "df['symbol'] = df['symbol'].str[:-4]\n", "df['symbol'] = df['symbol'].str.replace(r'(\\d{2})(\\w{3})(\\d{2})', r'\\3\\2', regex=True)\n", "df = df.sort_values(['time', 'symbol'])\n", "\n", "# df = df[df['symbol'].str.endswith(('CE', 'PE'))]\n", "df = df[df['symbol'].str.contains(month_name)] # Filter useless symbols\n", "# df = df[['time', 'symbol', 'price', 'volume', 'oi']]\n", "df['spot_symbol'] = df['symbol'].str.extract(r'^([A-Za-z&-]+)')\n", "df = df[~df['symbol'].str.contains('NIFTY')]\n", "# df = df[df['symbol']=='AARTIIND24FEB630PE']\n", "# df_all_stocks = df_all_stocks[df_all_stocks['spot_symbol']=='AARTIIND']\n", "# df = pd.merge(df, df_all_stocks[['time', 'spot_symbol', 'spot_price']], on=['time', 'spot_symbol'], how='left')\n", "# print(df)\n", "# print(df_all_stocks)\n", "df = df.merge(df_all_stocks, on=['time', 'spot_symbol'], how='left')\n", "df.rename(columns={'volume_x': 'volume', 'high_x': 'high', 'low_x': 'low'}, inplace=True)\n", "df = df[['time', 'symbol', 'spot_symbol', 'price', 'high', 'low', 'volume', 'oi', 'spot_price']]\n", "df = df[df['spot_price'].notnull()]\n", "df['ce_pe'] = df['symbol'].str[-2:]\n", "df['strike_price'] = df['symbol'].str.extract(r'(\\d+\\.?\\d*)(?=[CE|PE])').astype(float)\n", "df['traded_value'] = df['price'] * df['volume']\n", "print(df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_filtered = df\n", "df_filtered = df_filtered[~((df_filtered['ce_pe'] == 'PE') & (df_filtered['strike_price'] > 0.99 * df_filtered['spot_price']) | (df_filtered['ce_pe'] == 'CE') & (df_filtered['strike_price'] < 1.01 * df_filtered['spot_price']))]\n", "\n", "# df_filtered['prev_price'] = df_filtered.groupby('symbol')['price'].shift()\n", "\n", "df_filtered.loc[:, 'prev_price'] = df_filtered.groupby('symbol')['price'].shift()\n", "\n", "# df_filtered = df_filtered.assign(prev_price=df_filtered.groupby('symbol')['price'].shift())\n", "\n", "df_filtered['price_incr_5pc'] = df_filtered['price'] > 1.05 * df_filtered['prev_price']\n", "df_filtered['prev_prev_price'] = df_filtered.groupby('symbol')['prev_price'].shift()\n", "df_filtered['prev_price_incr_5pc'] = df_filtered['prev_price'] > 1.05 * df_filtered['prev_prev_price']\n", "df_filtered['prev_prev_prev_price'] = df_filtered.groupby('symbol')['prev_prev_price'].shift()\n", "df_filtered['prev_prev_price_incr_5pc'] = df_filtered['prev_prev_price'] > 1.05 * df_filtered['prev_prev_prev_price']\n", "df_filtered = df_filtered[df_filtered['price_incr_5pc'] & df_filtered['prev_price_incr_5pc'] & df_filtered['prev_prev_price_incr_5pc']]\n", "# df_filtered = df_filtered[df['symbol']=='DABUR24FEB570CE']\n", "# print(df_filtered['spot_symbol'].unique())\n", "\n", "# df_filtered['prev_time'] = df_filtered.groupby('symbol')['time'].shift()\n", "# df_filtered['prev_price'] = df_filtered.groupby('symbol')['price'].shift()\n", "\n", "# df_filtered = df_filtered.dropna(subset=['prev_price'])\n", "# df_filtered = df_filtered.dropna(subset=['oi'])\n", "# df_filtered = df_filtered[df_filtered['prev_price'] != 0]\n", "# df_filtered = df_filtered[df_filtered['oi'] != 0]\n", "\n", "# df_filtered['max_price'] = df_filtered.groupby(['symbol'])['price'].transform('max')\n", "# df_filtered['last_price'] = df_filtered.groupby(['symbol', df_filtered['time'].dt.date])['price'].transform('last')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ce_pe                                  current_ce_count  current_pe_count\n", "spot_symbol time                                                         \n", "IRCTC       2024-01-01 09:18:00+05:30               2.0               NaN\n", "TATAMOTORS  2024-01-01 09:20:00+05:30               5.0               NaN\n", "LICHSGFIN   2024-01-01 09:22:00+05:30               4.0               NaN\n", "EICHERMOT   2024-01-01 09:23:00+05:30               NaN               2.0\n", "OFSS        2024-01-01 09:23:00+05:30               2.0               NaN\n", "...                                                 ...               ...\n", "BANKBARODA  2024-01-01 15:24:00+05:30               NaN               1.0\n", "NATIONALUM  2024-01-01 15:24:00+05:30               NaN               1.0\n", "VEDL        2024-01-01 15:24:00+05:30               NaN               2.0\n", "BAJFINANCE  2024-01-01 15:26:00+05:30               NaN               1.0\n", "CHOLAFIN    2024-01-01 15:27:00+05:30               NaN               1.0\n", "\n", "[109 rows x 2 columns]\n"]}], "source": ["df_filtered2 = df_filtered\n", "\n", "# current_ce_pe_counts = df_filtered2.groupby(['spot_symbol', 'time', 'ce_pe']).size().unstack(fill_value=0)\n", "# current_ce_pe_counts.columns = ['current_ce_count', 'current_pe_count']\n", "\n", "current_ce_counts = df_filtered2[df_filtered2['ce_pe'] == 'CE'].groupby(['spot_symbol', 'time', 'ce_pe']).size().unstack(fill_value=0).rename(columns={'CE': 'current_ce_count'})\n", "current_pe_counts = df_filtered2[df_filtered2['ce_pe'] == 'PE'].groupby(['spot_symbol', 'time', 'ce_pe']).size().unstack(fill_value=0).rename(columns={'PE': 'current_pe_count'})\n", "current_ce_pe_counts = current_ce_counts.merge(current_pe_counts, on=['spot_symbol', 'time'], how='outer')\n", "\n", "print(current_ce_pe_counts.sort_values(by=['time', 'spot_symbol']))\n", "\n", "current_ce_pe_counts['cumulative_ce_count'] = current_ce_pe_counts.groupby('spot_symbol')['current_ce_count'].cumsum()\n", "current_ce_pe_counts['cumulative_pe_count'] = current_ce_pe_counts.groupby('spot_symbol')['current_pe_count'].cumsum()\n", "\n", "df_filtered2 = df_filtered2.merge(current_ce_pe_counts, on=['spot_symbol', 'time'], how='left')\n", "# print(df_filtered2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                       time spot_symbol  strike_price                symbol  \\\n", "0 2024-01-01 09:20:00+05:30  TATAMOTORS         835.0  TATAMOTORS24JAN835CE   \n", "1 2024-01-01 09:22:00+05:30   LICHSGFIN         570.0   LICHSGFIN24JAN570CE   \n", "2 2024-01-01 09:29:00+05:30   GUJGASLTD         480.0   GUJGASLTD24JAN480CE   \n", "3 2024-01-01 10:41:00+05:30    ADANIENT        3200.0   ADANIENT24JAN3200CE   \n", "\n", "   price   high    low  volume      oi  spot_price  ... prev_price  \\\n", "0  14.00  14.00  13.15   44175  370500      791.45  ...      13.20   \n", "1  14.30  14.30  13.25   36000  208000      549.25  ...      13.25   \n", "2  15.25  15.25  14.55  113750  528750      470.35  ...      14.25   \n", "3  85.00  86.25  80.45   80100  339600     2902.75  ...      80.45   \n", "\n", "   price_incr_5pc  prev_prev_price  prev_price_incr_5pc  prev_prev_prev_price  \\\n", "0            True            12.50                 True                  11.3   \n", "1            True            10.50                 True                   9.5   \n", "2            True            13.30                 True                  12.5   \n", "3            True            72.15                 True                  68.0   \n", "\n", "   prev_prev_price_incr_5pc  current_ce_count  current_pe_count  \\\n", "0                      True                 5                 0   \n", "1                      True                 4                 0   \n", "2                      True                 4                 0   \n", "3                      True                 4                 0   \n", "\n", "   cumulative_ce_count  cumulative_pe_count  \n", "0                    5                    0  \n", "1                    4                    0  \n", "2                    4                    0  \n", "3                    6                    0  \n", "\n", "[4 rows x 22 columns]\n"]}], "source": ["\n", "# df_filtered = df_filtered[df_filtered['spot_symbol']=='DABUR']\n", "df_filtered3 = df_filtered2[(df_filtered2['current_ce_count']>=3) & (df_filtered2['cumulative_ce_count']<=6) | (df_filtered2['current_pe_count']>=3) & (df_filtered2['cumulative_pe_count']<=6)]\n", "# df1 = df1[df1['spot_symbol']=='ABCAPITAL']\n", "# print(df1)\n", "\n", "# df_min_strike_ce = df1[df1['ce_pe'] == 'CE'].groupby(['time', 'spot_symbol'])['strike_price'].min().reset_index()\n", "\n", "\n", "df_ces = df_filtered3[df_filtered3['ce_pe'] == 'CE'].groupby(['time', 'spot_symbol'])['strike_price'].min().reset_index()\n", "df_ces = df_ces.merge(df_filtered3, on=['time', 'spot_symbol', 'strike_price'], how='left')\n", "\n", "df_pes = df_filtered3[df_filtered3['ce_pe'] == 'PE'].groupby(['time', 'spot_symbol'])['strike_price'].max().reset_index()\n", "df_pes = df_pes.merge(df_filtered3, on=['time', 'spot_symbol', 'strike_price'], how='left')\n", "\n", "df_buys = pd.concat([df_ces, df_pes]).sort_values(by=['time', 'symbol'])\n", "\n", "df_buys = df_buys.sort_values(by='time').drop_duplicates(subset='spot_symbol', keep='first')\n", "print(df_buys)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Percentage of rows where sell_price is greater than buy_price: nan%\n", "Empty DataFrame\n", "Columns: [time, symbol, price, sell_price, sell_time]\n", "Index: []\n"]}], "source": ["df_buys2 = df_buys\n", "df2 = df.sort_values(by='time')\n", "for index, row in df_buys2.iterrows():\n", "    df2.loc[(df2['time'] == row['time']) & (df2['symbol'] == row['symbol']), 'is_buy'] = True\n", "\n", "for index, row in df_buys2.iterrows():\n", "    index_in_df2 = df2[(df2['time'] == row['time']) & (df2['symbol'] == row['symbol'])].index\n", "    df2.loc[index_in_df2, 'is_buy'] = True\n", "    for next_index, next_row in df2[(df2['symbol'] == row['symbol']) & (df2['time']>row['time'])].iterrows():\n", "        if next_row['price'] >= row['high'] * 1.1:\n", "            df2.loc[index_in_df2, 'sell_time'] = next_row['time']\n", "            df2.loc[index_in_df2, 'sell_price'] = next_row['price']\n", "            break\n", "        elif next_row['price'] <= row['low']*0.9:\n", "            df2.loc[index_in_df2, 'sell_time'] = next_row['time']\n", "            df2.loc[index_in_df2, 'sell_price'] = next_row['price']\n", "            break\n", "    \n", "\n", "df2 = df2[df2['is_buy']==True]\n", "df2 = df2[['time', 'symbol', 'price', 'sell_price', 'sell_time']]\n", "\n", "\n", "percentage = (df2['sell_price'] > df2['price']).mean()\n", "print(f\"Percentage of rows where sell_price is greater than buy_price: {percentage * 100:.2f}%\")\n", "print(df2)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "algo", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 2}