import pika
import json
from broker import Zerodha
import utils
import logging
import psycopg2
from filtered_instruments import FilteredInstruments
from telegram_utils import TelegramUtil
from datetime import datetime
import time
import asyncio
import traceback
from broker_singleton import BrokerSingleton
import re

logging.basicConfig(level=logging.INFO,
                    format="%(asctime)s %(levelname)s: %(message)s",
                    datefmt="%Y-%m-%d %H:%M:%S")


class TradingRules:
    """Handles all trading validation and rule checking logic"""

    def __init__(self, db_conn, filtered_instruments):
        self.db_conn = db_conn
        self.filtered_instruments = filtered_instruments

    def is_market_indicative(self, tradingsymbol, spot_symbol):
        """Check if market conditions are favorable for trading"""
        with self.db_conn.cursor() as cur:
            query = f"""
                select * from ohlc_1hour where symbol = '{spot_symbol}' order by time desc limit 1
            """
            cur.execute(query)
            row = cur.fetchone()
            price = row[2]
            vwap = row[5]
            ema9 = row[6]
            ema20 = row[7]
            price_change = row[8]
            print(f"🔍 MARKET_INDICATIVE → Symbol: {tradingsymbol} | Spot: {spot_symbol} | Price: {price} | VWAP: {vwap} | EMA9: {ema9} | EMA20: {ema20} | Change: {price_change}")

        if tradingsymbol.endswith('CE'):
            return price>vwap or ema9>ema20
        elif tradingsymbol.endswith('PE'):
            return price<vwap or ema9<ema20
        else:
            return True

    def is_trading_time_valid(self):
        """Check if current time is within trading hours"""
        current_time = datetime.now().time()
        return not (current_time > datetime.strptime("14:30", "%H:%M").time()
                   or current_time < datetime.strptime("09:30", "%H:%M").time())

    def has_unusual_activity_in_option_group(self, spot_symbol, symbols, positions, orders):
        """Main trading validation logic"""
        current_ce_count = len([symbol for symbol in symbols if symbol['symbol'].endswith('CE')])
        current_pe_count = len([symbol for symbol in symbols if symbol['symbol'].endswith('PE')])

        with self.db_conn.cursor() as cur:
            query = f"""
                SELECT
                    (SELECT COUNT(*) FROM unusual_option_activity WHERE spot_symbol = '{spot_symbol}' and strike_symbol LIKE '%CE' and alerted_at > CURRENT_DATE) AS ce_count,
                    (SELECT COUNT(*) FROM unusual_option_activity WHERE spot_symbol = '{spot_symbol}' and strike_symbol LIKE '%PE' and alerted_at > CURRENT_DATE) AS pe_count,
                    (SELECT spot_price FROM unusual_option_activity WHERE spot_symbol = '{spot_symbol}' order by alerted_at desc limit 1) AS latest_spot_price,
                    (SELECT spot_price FROM unusual_option_activity WHERE spot_symbol = '{spot_symbol}' and alerted_at > CURRENT_DATE order by alerted_at asc limit 1) AS initial_spot_price
            """
            cur.execute(query)
            total_ce_count, total_pe_count, latest_spot_price, initial_spot_price = cur.fetchone()
            print("spot_symbol: ", spot_symbol, "symbols: ", [(s['symbol'], s['price'], s['vwap'], s['ema9'], s['ema20']) for s in symbols], "total_ce_count: ", total_ce_count, "total_pe_count: ", total_pe_count, "current_ce_count:", current_ce_count, "current_pe_count: ", current_pe_count, "latest_spot_price: ", latest_spot_price, "initial_spot_price: ", initial_spot_price)

        # Volume and count validation rules
        if not ((current_ce_count >= 3 and total_pe_count==0 and total_ce_count-current_ce_count<=2)
            or (current_pe_count >= 3 and total_ce_count == 0 and total_pe_count-current_pe_count<=2)
            or (total_ce_count >= 5 and total_pe_count == 0 and latest_spot_price>initial_spot_price and ((latest_spot_price - initial_spot_price) / initial_spot_price) <= 0.02)
            or (total_pe_count >= 5 and total_ce_count == 0 and initial_spot_price>latest_spot_price and ((initial_spot_price - latest_spot_price) / initial_spot_price) <= 0.02)
        ):
            return False

        # Check existing positions and orders
        position_symbols = [p['tradingsymbol'] for p in positions]
        order_symbols = [p['tradingsymbol'] for p in orders]
        for p in position_symbols:
            if p.startswith(spot_symbol):
                return False
        for o in order_symbols:
            if o.startswith(spot_symbol):
                return False

        # Validate symbols and technical indicators
        valid_symbols = [v for v in symbols if v]
        if not valid_symbols:
            return False

        picked_symbol = valid_symbols[-1] if valid_symbols[0]['symbol'].endswith('PE') else valid_symbols[0]

        print(f"📊 INDICATOR_CHECKS → Symbol: {picked_symbol['symbol']} | Price: {picked_symbol['price']} | VWAP: {picked_symbol['vwap']} | EMA9: {picked_symbol['ema9']} | EMA20: {picked_symbol['ema20']}")
        if picked_symbol['price']<picked_symbol['vwap'] and picked_symbol['ema9']<picked_symbol['ema20']:
            return False

        return True


class OrderExecutor:
    """Handles order placement and execution logic"""

    def __init__(self, broker, config, filtered_instruments):
        self.broker = broker
        self.config = config
        self.filtered_instruments = filtered_instruments

    def calculate_order_params(self, tradingsymbol, price):
        """Calculate order parameters like quantity, adjusted price"""
        script_lot_size = self.filtered_instruments.loc[self.filtered_instruments['tradingsymbol'] == tradingsymbol, 'lot_size'].values[0]
        script_tick_size = self.filtered_instruments.loc[self.filtered_instruments['tradingsymbol'] == tradingsymbol, 'tick_size'].values[0]
        adjusted_price = int(price/script_tick_size)*script_tick_size

        live_balance = 1000000  # self.broker.get_margin()['available']['live_balance']
        capital_per_trade = min(self.config.capital, live_balance)
        script_freeze_limit = self.filtered_instruments.loc[self.filtered_instruments['tradingsymbol'] == tradingsymbol, 'freeze_qty'].values[0]
        qty = min(int((capital_per_trade/adjusted_price)/script_lot_size)*script_lot_size, script_freeze_limit)

        product = 'MIS' if tradingsymbol.endswith('CE') or tradingsymbol.endswith('PE') else 'CNC'
        exchange = 'NFO' if tradingsymbol.endswith('CE') or tradingsymbol.endswith('PE') else 'NSE'

        return {
            'qty': qty,
            'price': adjusted_price,
            'product': product,
            'exchange': exchange,
            'tick_size': script_tick_size,
            'lot_size': script_lot_size
        }

    def place_buy_order(self, tradingsymbol, price):
        """Execute buy order"""
        order_params = self.calculate_order_params(tradingsymbol, price)

        if order_params['qty'] == 0:
            print("Not enough balance")
            return False

        print(datetime.now(), " ", "placing_order_for: "+tradingsymbol+' @'+str(order_params['price'])+ ' for size '+str(order_params['qty']))

        try:
            self.broker.place_order(
                tradingsymbol=tradingsymbol,
                transaction_type='BUY',
                quantity=order_params['qty'],
                product=order_params['product'],
                order_type='LIMIT',
                price=order_params['price'],
                exchange=order_params['exchange']
            )
            return True
        except:
            print(traceback.format_exc())
            return False

    def place_sell_order(self, tradingsymbol, price, qty):
        """Execute sell order"""
        script_tick_size = self.filtered_instruments.loc[self.filtered_instruments['tradingsymbol'] == tradingsymbol, 'tick_size'].values[0]
        adjusted_price = int(price*0.9/script_tick_size)*script_tick_size

        try:
            self.broker.place_order(
                tradingsymbol=tradingsymbol,
                transaction_type='SELL',
                quantity=qty,
                product='MIS',
                order_type='LIMIT',
                price=adjusted_price,
                exchange='NFO'
            )
            return True
        except:
            print(traceback.format_exc())
            return False


class MessagingService:
    """Handles all messaging and notification logic"""

    def __init__(self, config):
        self.telegram_bot = TelegramUtil(config=config, client=False, bot=True)
        self.telegram_chat_id = -1002075758731

    async def send_trade_alert(self, tradingsymbol, price):
        """Send trading alert via Telegram"""
        msg = f"Alert: {tradingsymbol} @ {round(price, 2)}, SL: {round(price*0.8, 2)}"
        await self.telegram_bot.send_message(chatid=self.telegram_chat_id, msg=msg)


class UnusualVolumeConsumer:
    """Main consumer class that orchestrates trading operations"""

    def __init__(self):
        self.config = utils.get_config()
        self.broker = BrokerSingleton()
        self.filtered_instruments = FilteredInstruments(self.broker).df_raw
        self.db_conn = psycopg2.connect(
            host=self.config.db_host,
            database=self.config.db_name,
            user=self.config.db_user,
            port=self.config.db_port
        )

        # Initialize separated components
        self.trading_rules = TradingRules(self.db_conn, self.filtered_instruments)
        self.order_executor = OrderExecutor(self.broker, self.config, self.filtered_instruments)
        self.messaging_service = MessagingService(self.config)

        self.buy_alert_query = """
            INSERT INTO daily_option_trades (time, symbol, buy_price) VALUES (%s, %s, %s)
        """

    def get_buyable_quote(self, q):
        """Find the most liquid OTM option to buy based on the spot price."""
        print("Inside buyable quote")
        spot_symbol = q['spot_symbol']
        symbol = q['symbol']
        option_type = q['symbol'][-2:]

        # 2. Fetch the last known spot price from the database
        with self.db_conn.cursor() as cur:
            spot_price_query = f"""
                SELECT spot_price FROM unusual_option_activity WHERE spot_symbol = '{spot_symbol}' order by alerted_at desc limit 1
            """
            cur.execute(spot_price_query)
            spot_price_row = cur.fetchone()
            if not spot_price_row:
                return None
            spot_price = spot_price_row[0]

        # 3. Identify the relevant option chain
        option_chain = self.filtered_instruments[
            (self.filtered_instruments['name'] == spot_symbol) &
            (self.filtered_instruments['instrument_type'] == option_type)
        ].copy()

        if option_chain.empty:
            return None

        # 4. Filter for Out-of-the-Money (OTM) Options
        if option_type == 'CE':
            otm_options = option_chain[option_chain['strike'] > spot_price]
        else:  # PE
            otm_options = option_chain[option_chain['strike'] < spot_price]

        if otm_options.empty:
            return None
        otm_symbols = tuple(otm_options['tradingsymbol'].to_list())
        if not otm_symbols:
            return None
        # 6. Return the cloned quote with the new symbol
        with self.db_conn.cursor() as cur:
            # Use DISTINCT ON to get the single latest record for each symbol
            oi_query = f"""
                SELECT DISTINCT ON (symbol) symbol, oi, price
                FROM ohlc_1min
                WHERE symbol IN %s
                ORDER BY symbol, time DESC;
            """
            cur.execute(oi_query, (otm_symbols,))
            oi_results = cur.fetchall()

        if not oi_results:
            return None

        # Find the symbol with the maximum OI from the results
        most_liquid_symbol, _, price = max(oi_results, key=lambda item: item[1])

        # 6. Return the cloned quote with the new, most liquid symbol
        new_q = q.copy()
        new_q['symbol'] = most_liquid_symbol
        new_q['price'] = price
        return new_q

    def buy(self, tradingsymbol, spot_symbol, price):
        """Main buy logic that runs execution"""
        print("tradingsymbol: ", tradingsymbol, "price: ", price)

        # Send alert message
        loop = asyncio.get_event_loop()
        loop.run_until_complete(self.messaging_service.send_trade_alert(tradingsymbol, price))

        # Execute order
        self.order_executor.place_buy_order(tradingsymbol, price)

    def sell(self, tradingsymbol, price, qty):
        """Sell order execution"""
        return self.order_executor.place_sell_order(tradingsymbol, price, qty)


    def get_position_symbol_against_spot(self, spot_symbol, positions):
        """Get existing position for a spot symbol"""
        return next(((p['tradingsymbol'], p['quantity'], p['last_price']) for p in positions if p['tradingsymbol'].startswith(spot_symbol) and p['quantity']!=0), (None, None, None))

    def handle_conflicting_signal(self, spot_symbol, position_symbol_against_spot, position_symbol_against_spot_qty, position_symbol_against_spot_last_price, orders):
        """Handle conflicting trading signals by canceling orders and selling positions"""
        script_tick_size = self.filtered_instruments.loc[self.filtered_instruments['tradingsymbol'] == position_symbol_against_spot, 'tick_size'].values[0]
        print('Found a conflicting signal: ', position_symbol_against_spot)
        print('Cancel all pending orders and sell asap: ', position_symbol_against_spot)

        # Cancel existing SL orders
        open_sl_order_id = next((o['order_id'] for o in orders if o['transaction_type'] == 'SELL' and o['status'] == 'OPEN' and o['order_type']=='SL' and o['tradingsymbol'] == position_symbol_against_spot), None)
        if open_sl_order_id:
            self.broker.cancel_order(order_id=open_sl_order_id)
            time.sleep(1)

        # Sell the position
        self.sell(tradingsymbol=position_symbol_against_spot, price=int(position_symbol_against_spot_last_price * 0.9 / script_tick_size) * script_tick_size, qty=position_symbol_against_spot_qty)

    def save_trade_alerts(self, tradeable_quotes):
        """Save trade alerts to database"""
        try:
            with self.db_conn.cursor() as cur:
                data = [(datetime.now().replace(second=0, microsecond=0), row['symbol'], row['price']) for row in tradeable_quotes]
                cur.executemany(self.buy_alert_query, data)
                self.db_conn.commit()
        except Exception as e:
            print(traceback.format_exc())

    def process_events(self):
        """Main event processing loop for consuming RabbitMQ messages"""
        # Connect to RabbitMQ
        connection = pika.BlockingConnection(pika.ConnectionParameters('localhost', heartbeat=10))
        channel = connection.channel()

        # Declare the same queue as in the producer
        channel.queue_declare(queue='quotes')

        # Define a callback function that will be called when a message is received
        def callback(_, __, ___, body):
            quotes = json.loads(body)
            print("\n\n", datetime.now(), "received_trades: ", [q['symbol'] for q in quotes])

            # Group quotes by spot symbol
            spot_symbol_dict = self._group_quotes_by_spot_symbol(quotes)

            # Get current positions and orders
            positions = self.broker.positions()
            orders = self.broker.orders()

            # Process each spot symbol
            tradeable_spots = {}
            for spot_symbol, symbols in spot_symbol_dict.items():
                # Check for existing positions
                position_symbol_against_spot, position_symbol_against_spot_qty, position_symbol_against_spot_last_price = self.get_position_symbol_against_spot(spot_symbol, positions)

                # Handle conflicting signals
                if self._has_conflicting_signal(position_symbol_against_spot, symbols):
                    self.handle_conflicting_signal(spot_symbol, position_symbol_against_spot, position_symbol_against_spot_qty, position_symbol_against_spot_last_price, orders)

                # Check if multiple symbols have shown unusual option activity for same spot 
                if self.trading_rules.has_unusual_activity_in_option_group(spot_symbol, symbols, positions, orders):
                    tradeable_spots[spot_symbol] = symbols

            # Select tradeable quotes
            tradeable_quotes = [v[-1] if v[0]['symbol'].endswith('PE') else v[0] for v in tradeable_spots.values()]


            if not self.trading_rules.is_trading_time_valid():
                return
            market_indicative_quotes = [q for q in tradeable_quotes if self.trading_rules.is_market_indicative(q['symbol'], q['spot_symbol'])]
            buyable_quotes = [self.get_buyable_quote(q) for q in market_indicative_quotes]
            self.save_trade_alerts(buyable_quotes)
            # Execute trades
            for q in buyable_quotes:
                self.buy(tradingsymbol=q['symbol'], spot_symbol=q['spot_symbol'], price=q['price'])


        # Start consuming messages
        channel.basic_consume(queue='quotes', on_message_callback=callback, auto_ack=True)
        channel.start_consuming()

    def _group_quotes_by_spot_symbol(self, quotes):
        """Group quotes by spot symbol"""
        spot_symbol_dict = {}
        for q in quotes:
            spot_symbol = q["spot_symbol"]
            symbol = q["symbol"]
            price = q['price_to']
            vwap = q['vwap']
            ema9 = q['ema9']
            ema20 = q['ema20']

            symbol_data = {"symbol": symbol, "price": price, "spot_symbol": spot_symbol, "vwap": vwap, "ema9": ema9, "ema20": ema20}

            if spot_symbol in spot_symbol_dict:
                spot_symbol_dict[spot_symbol].append(symbol_data)
            else:
                spot_symbol_dict[spot_symbol] = [symbol_data]

        return spot_symbol_dict

    def _has_conflicting_signal(self, position_symbol_against_spot, symbols):
        """Check if there's a conflicting signal between position and new symbols"""
        if not position_symbol_against_spot:
            return False

        return any(
            (position_symbol_against_spot.endswith('CE') and s['symbol'].endswith('PE')) or
            (position_symbol_against_spot.endswith('PE') and s['symbol'].endswith('CE'))
            for s in symbols
        )

if __name__ == "__main__":
    # time.sleep(30)
    print(__file__)
    uvc = UnusualVolumeConsumer()
    fi = FilteredInstruments(uvc.broker)
    uvc.process_events()

