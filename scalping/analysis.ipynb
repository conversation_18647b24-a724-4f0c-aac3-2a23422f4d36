{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import calendar\n", "import psycopg2\n", "import utils\n", "import datetime\n", "import warnings\n", "import pandas as pd\n", "import numpy as np\n", "import math\n", "\n", "pd.options.mode.copy_on_write = True\n", "\n", "# warnings.simplefilter(action='ignore', category=FutureWarning)\n", "import pandas\n", "\n", "class DataAnalyzer:\n", "    def __init__(self) -> None:\n", "        config = utils.get_config()\n", "        self.db_conn = psycopg2.connect(\n", "            host=config.db_host,\n", "            database=config.db_name,\n", "            user=config.db_user,\n", "            port=config.db_port\n", "        )\n", "        self.df_all_stocks = pd.read_pickle('2024/fno_stocks_1min_data/all_stocks.pkl')\n", "        # self.df_all_stocks = pd.read_csv('2024/fno_stocks_1min_data/all_stocks.csv')\n", "        # self.df_all_stocks.rename(columns={'ticker': 'spot_symbol', 'date': 'time', 'close': 'spot_price'}, inplace=True)\n", "        # self.df_all_stocks['time'] = pd.to_datetime(self.df_all_stocks['time'], format='%Y-%m-%d %H:%M:%S%z')\n", "\n", "    def get_trading_days(self, year, month, date_starting_from=None):\n", "        # Get all the dates in a given month and year\n", "        def get_last_thursday(year, month):\n", "            cal = calendar.monthcalendar(year, month)\n", "            last_week = cal[-1]\n", "            if last_week[calendar.THURSDAY] != 0:\n", "                last_thursday = last_week[calendar.THURSDAY]\n", "            else:\n", "                last_thursday = cal[-2][calendar.THURSDAY]\n", "            return last_thursday\n", "\n", "        dates = []\n", "        num_days = calendar.monthrange(year, month)[1]\n", "        for day in range(1, num_days + 1):\n", "            date = datetime.date(year, month, day)\n", "            dates.append(date)\n", "        holidays = ['2023-01-26', '2023-03-07', '2023-03-30', '2023-04-04', '2023-04-07', '2023-04-14', '2023-05-01', '2023-06-29', '2023-08-15', '2023-09-19', '2023-10-02', '2023-10-24', '2023-11-14', '2023-11-27', '2023-12-25', '2024-01-22', '2024-01-26', '2024-03-08', '2024-03-25', '2024-03-29', '2024-04-11', '2024-04-17', '2024-05-01', '2024-06-17', '2024-07-17', '2024-08-15', '2024-10-02', '2024-11-01', '2024-11-15', '2024-12-25']\n", "        trading_days = [d for d in dates if d.weekday() < 5 and str(d) not in holidays]\n", "        if date_starting_from:\n", "            trading_days = [t for t in trading_days if t.day>=date_starting_from]\n", "        last_thursday = get_last_thursday(year, month)\n", "        trading_days = [t for t in trading_days if t.day<=last_thursday]\n", "        return trading_days\n", "\n", "\n", "    def get_transformed_df(self, day, month, year):\n", "        month_name = calendar.month_abbr[month].upper()\n", "        df_file_name = f'/Users/<USER>/Downloads/2024/{month:02d}{year:04d}/GFDLNFO_BACKADJUSTED_{day:02d}{month:02d}{year:04d}.pkl'\n", "        df = pd.read_pickle(df_file_name)\n", "        # print(\"plain_df\", df)\n", "        df['time'] = pd.to_datetime(df['Date'] + ' ' + df['Time']+'+05:30', format='%d/%m/%Y %H:%M:%S%z')\n", "        df['time'] = df['time'] - pd.<PERSON><PERSON><PERSON>(seconds=59)\n", "        df.rename(columns={'Ticker': 'symbol', 'Close': 'price', 'High': 'high', 'Low': 'low', 'Volume': 'volume', 'Open Interest': 'oi'}, inplace=True)\n", "        df['symbol'] = df['symbol'].str[:-4]\n", "        df['symbol'] = df['symbol'].str.replace(r'(\\d{2})(\\w{3})(\\d{2})', r'\\3\\2', regex=True)\n", "        df = df.sort_values(['time', 'symbol'])\n", "        df = df[df['symbol'].str.endswith(('CE', 'PE'))]\n", "        df = df[df['symbol'].str.contains(month_name)] # Filter useless symbols\n", "        df['spot_symbol'] = df['symbol'].str.extract(r'^([A-Za-z&-]+)')\n", "        df['ce_pe'] = df['symbol'].str[-2:]\n", "        df['strike_price'] = df['symbol'].str.extract(r'(\\d+\\.?\\d*)(?=[CE|PE])').astype(float)\n", "        df = df[~df['symbol'].str.contains('NIFTY')]\n", "        df = df.merge(self.df_all_stocks, on=['time', 'spot_symbol'], how='left')\n", "        df.rename(columns={'volume_x': 'volume', 'high_x': 'high', 'low_x': 'low'}, inplace=True)\n", "        df = df[['time', 'symbol', 'spot_symbol', 'price', 'high', 'low', 'volume', 'oi', 'spot_price']]\n", "        df = df[df['spot_price'].notnull()]\n", "        df['ce_pe'] = df['symbol'].str[-2:]\n", "        df['strike_price'] = df['symbol'].str.extract(r'(\\d+\\.?\\d*)(?=[CE|PE])').astype(float)\n", "        df['traded_value'] = df['price'] * df['volume']\n", "        return df\n", "\n", "    def pre_filtering(self, df):\n", "        return df[~((df['ce_pe'] == 'PE') & (df['strike_price'] > 0.99 * df['spot_price']) | (df['ce_pe'] == 'CE') & (df['strike_price'] < 1.01 * df['spot_price']))]\n", "\n", "    def post_strategy(self, df):\n", "\n", "        # current_ce_pe_counts = df.groupby(['spot_symbol', 'time', 'ce_pe']).size().unstack(fill_value=0)\n", "        # current_ce_pe_counts.columns = ['current_ce_count', 'current_pe_count']\n", "\n", "        current_ce_counts = df[df['ce_pe'] == 'CE'].groupby(['spot_symbol', 'time', 'ce_pe']).size().unstack(fill_value=0).rename(columns={'CE': 'current_ce_count'})\n", "        current_pe_counts = df[df['ce_pe'] == 'PE'].groupby(['spot_symbol', 'time', 'ce_pe']).size().unstack(fill_value=0).rename(columns={'PE': 'current_pe_count'})\n", "        current_ce_pe_counts = current_ce_counts.merge(current_pe_counts, on=['spot_symbol', 'time'], how='outer').fillna(0)\n", "\n", "        if 'current_ce_count' not in current_ce_pe_counts.columns:\n", "            current_ce_pe_counts['current_ce_count'] = 0\n", "        if 'current_pe_count' not in current_ce_pe_counts.columns:\n", "            current_ce_pe_counts['current_pe_count'] = 0\n", "        # print(current_ce_counts)\n", "        # print(current_pe_counts)\n", "\n", "        current_ce_pe_counts['cumulative_ce_count'] = current_ce_pe_counts.groupby('spot_symbol')['current_ce_count'].cumsum()\n", "        current_ce_pe_counts['cumulative_pe_count'] = current_ce_pe_counts.groupby('spot_symbol')['current_pe_count'].cumsum()\n", "\n", "        # print(current_ce_pe_counts)\n", "\n", "        df = df.merge(current_ce_pe_counts, on=['spot_symbol', 'time'], how='left')\n", "        df = df[((df['current_ce_count']>=3) & (df['cumulative_ce_count']<=6) & (df['cumulative_pe_count']==0)) | ((df['current_pe_count']>=3) & (df['cumulative_pe_count']<=6) & (df['cumulative_ce_count']==0))]\n", "        return df\n", "\n", "    def clean_duplicated(self, df1):\n", "        df_ces = df1[df1['ce_pe'] == 'CE'].groupby(['time', 'spot_symbol'])['strike_price'].min().reset_index()\n", "        df_ces = df_ces.merge(df1, on=['time', 'spot_symbol', 'strike_price'], how='left')\n", "        df_pes = df1[df1['ce_pe'] == 'PE'].groupby(['time', 'spot_symbol'])['strike_price'].max().reset_index()\n", "        df_pes = df_pes.merge(df1, on=['time', 'spot_symbol', 'strike_price'], how='left')\n", "        df_buys = pd.concat([df_ces, df_pes]).sort_values(by=['time', 'symbol'])\n", "        df_buys = df_buys.sort_values(by='time').drop_duplicates(subset='spot_symbol', keep='first')\n", "        return df_buys\n", "\n", "    def get_pnl2(self, df_buys, df):\n", "        df = df.sort_values(by='time')\n", "        df['is_buy'] = np.nan\n", "        df['sell_time'] = np.nan\n", "        df['sell_price'] = np.nan\n", "        for _, row in df_buys.iterrows():\n", "            df.loc[(df['time'] == row['time']) & (df['symbol'] == row['symbol']), 'is_buy'] = True\n", "\n", "        for _, row in df_buys.iterrows():\n", "            sl_price = 0.9*row['price']\n", "            target_price = 1.1*row['price']\n", "            index_in_df = df[(df['time'] == row['time']) & (df['symbol'] == row['symbol'])].index\n", "            df.loc[index_in_df, 'is_buy'] = True\n", "            for _, next_row in df[(df['symbol'] == row['symbol']) & (df['time']>row['time'])].iterrows():\n", "                if next_row['low'] <=sl_price:\n", "                    df.loc[index_in_df, 'sell_time'] = next_row['time']\n", "                    df.loc[index_in_df, 'sell_price'] = sl_price\n", "                    break\n", "                elif next_row['high'] > 1.1*target_price:\n", "                    df.loc[index_in_df, 'sell_time'] = next_row['time']\n", "                    df.loc[index_in_df, 'sell_price'] = target_price\n", "            \n", "            if pd.isnull(df.loc[index_in_df, 'sell_price']):\n", "                last_price = df[df['symbol'] == row['symbol']]['price'].iloc[-1]\n", "                df.loc[index_in_df, 'sell_price'] = last_price\n", "                last_time = df[df['symbol'] == row['symbol']]['time'].iloc[-1]\n", "                df.loc[index_in_df, 'sell_time'] = last_time\n", "\n", "        df = df[df['is_buy']==True]\n", "        df = df[['time', 'symbol', 'price', 'sell_price', 'sell_time']]\n", "\n", "        # Set sell_price to buy_price if couldn't sell\n", "        df.loc[df['sell_price'].isnull(), 'sell_price'] = df['price']\n", "        df.loc[df['sell_time'].isnull(), 'sell_time'] = df['time']\n", "\n", "        df['pnl'] = (df['sell_price'] - df['price']) / df['price'] * 100\n", "        return df\n", "\n", "\n", "    def get_pnl(self, df_buys, df):\n", "        df = df.sort_values(by='time')\n", "        df['is_buy'] = np.nan\n", "        df['sell_time'] = np.nan\n", "        df['sell_price'] = np.nan\n", "        for _, row in df_buys.iterrows():\n", "            df.loc[(df['time'] == row['time']) & (df['symbol'] == row['symbol']), 'is_buy'] = True\n", "\n", "        for _, row in df_buys.iterrows():\n", "            sl_price = 0.95*row['price']\n", "\n", "            index_in_df = df[(df['time'] == row['time']) & (df['symbol'] == row['symbol'])].index\n", "            df.loc[index_in_df, 'is_buy'] = True\n", "            for _, next_row in df[(df['symbol'] == row['symbol']) & (df['time']>row['time'])].iterrows():\n", "                if sl_price>=next_row['low']:\n", "                    df.loc[index_in_df, 'sell_time'] = next_row['time']\n", "                    df.loc[index_in_df, 'sell_price'] = sl_price\n", "                    break\n", "                elif sl_price < next_row['high']*0.95:\n", "                    sl_price = next_row['high']*0.95\n", "\n", "        df = df[df['is_buy']==True]\n", "        df = df[['time', 'symbol', 'price', 'sell_price', 'sell_time']]\n", "\n", "        # Set sell_price to buy_price if couldn't sell\n", "        df.loc[df['sell_price'].isnull(), 'sell_price'] = df['price']\n", "        df.loc[df['sell_time'].isnull(), 'sell_time'] = df['time']\n", "\n", "        df['pnl'] = (df['sell_price'] - df['price']) / df['price'] * 100\n", "        return df\n", "\n", "    def net_pnl(self, df):\n", "        net_pnl = df['pnl'].sum()\n", "        return net_pnl\n", "\n", "    # Consecutive 3 candles with more than 5% increase\n", "    def get_df_passing_strategy_3_consecutive_incr_5_perc(self, df):\n", "        df_filtered = df\n", "        df_filtered['prev_price'] = df_filtered.groupby('symbol')['price'].shift()\n", "        df_filtered['price_incr_5pc'] = df_filtered['price'] > 1.05 * df_filtered['prev_price']\n", "        df_filtered['prev_prev_price'] = df_filtered.groupby('symbol')['prev_price'].shift()\n", "        df_filtered['prev_price_incr_5pc'] = df_filtered['prev_price'] > 1.05 * df_filtered['prev_prev_price']\n", "        df_filtered['prev_prev_prev_price'] = df_filtered.groupby('symbol')['prev_prev_price'].shift()\n", "        df_filtered['prev_prev_price_incr_5pc'] = df_filtered['prev_prev_price'] > 1.05 * df_filtered['prev_prev_prev_price']\n", "\n", "        df_filtered = df_filtered[df_filtered['price_incr_5pc'] & df_filtered['prev_price_incr_5pc'] & df_filtered['prev_prev_price_incr_5pc']]\n", "        return df_filtered\n", "\n", "    # Traded value > 500000\n", "    def get_df_passing_strategy_traded_val_gt_500000(self, df):\n", "        df['prev_price'] = df.groupby('symbol')['price'].shift()\n", "        df = df[(df['price']>df['prev_price']*1.01) & (df['traded_value']>=500000)]\n", "        return df\n", "\n", "    # Traded value > 1000000\n", "    def get_df_passing_strategy_traded_val_gt_1000000(self, df):\n", "        df['prev_price'] = df.groupby('symbol')['price'].shift()\n", "        df = df[(df['price']>df['prev_price']*1.01) & (df['traded_value']>=1000000)]\n", "        return df\n", "\n", "    # Candle delta > 50000 and price_incr more than 1%\n", "    def get_df_passing_strategy_candle_delta_gt_50000_and_price_incr_1_perc(self, df):\n", "        df['prev_price'] = df.groupby('symbol')['price'].shift()\n", "        df = df[(df['price']>df['prev_price']*1.01) & ((df['price']-df['prev_price'])*df['volume']>=50000)]\n", "        return df\n", "\n", "    # Candle delta > 50000 and price_incr more than 5%\n", "    def get_df_passing_strategy_candle_delta_gt_50000_and_price_incr_5_perc(self, df):\n", "        df['prev_price'] = df.groupby('symbol')['price'].shift()\n", "        df = df[(df['price']>df['prev_price']*1.05) & ((df['price']-df['prev_price'])*df['volume']>=50000)]\n", "        return df\n", "\n", "    # Candle delta > 50000 and price_incr more than 10%\n", "    def get_df_passing_strategy_candle_delta_gt_50000_and_price_incr_10_perc(self, df):\n", "        df['prev_price'] = df.groupby('symbol')['price'].shift()\n", "        df = df[(df['price']>df['prev_price']*1.1) & ((df['price']-df['prev_price'])*df['volume']>=50000)]\n", "        return df\n", "\n", "    # Candle delta > 50000 and price_incr more than 5% and only ce symbols\n", "    def get_df_passing_strategy_candle_delta_gt_50000_and_price_incr_5_perc_ce(self, df):\n", "        df['prev_price'] = df.groupby('symbol')['price'].shift()\n", "        df = df[(df['price']>df['prev_price']*1.05) & ((df['price']-df['prev_price'])*df['volume']>=50000) & (df['ce_pe']=='CE')]\n", "        return df\n", "\n", "    # Candle delta > 50000 and price_incr more than 10% and only pe symbols\n", "    def get_df_passing_strategy_candle_delta_gt_50000_and_price_incr_10_perc_ce(self, df):\n", "        df['prev_price'] = df.groupby('symbol')['price'].shift()\n", "        df = df[(df['price']>df['prev_price']*1.1) & ((df['price']-df['prev_price'])*df['volume']>=50000) & (df['ce_pe']=='CE')]\n", "        return df\n", "\n", "    # Candle delta > 100000 and price_incr more than 5% and only ce symbols\n", "    def get_df_passing_strategy_candle_delta_gt_100000_and_price_incr_5_perc_ce(self, df):\n", "        df['prev_price'] = df.groupby('symbol')['price'].shift()\n", "        df = df[(df['price']>df['prev_price']*1.05) & ((df['price']-df['prev_price'])*df['volume']>=100000) & (df['ce_pe']=='CE')]\n", "        return df\n", "\n", "    # Candle delta > 50000 and price_incr more than 10% and only pe symbols\n", "    def get_df_passing_strategy_candle_delta_gt_100000_and_price_incr_10_perc_ce(self, df):\n", "        df['prev_price'] = df.groupby('symbol')['price'].shift()\n", "        df = df[(df['price']>df['prev_price']*1.1) & ((df['price']-df['prev_price'])*df['volume']>=100000) & (df['ce_pe']=='CE')]\n", "        return df\n", "\n", "\n", "    # Candle delta > 50000 and price_incr more than 5% and only pe symbols\n", "    def get_df_passing_strategy_candle_delta_gt_50000_and_price_incr_5_perc_pe(self, df):\n", "        df['prev_price'] = df.groupby('symbol')['price'].shift()\n", "        df = df[(df['price']>df['prev_price']*1.05) & ((df['price']-df['prev_price'])*df['volume']>=50000) & (df['ce_pe']=='PE')]\n", "        return df\n", "\n", "    # Candle delta > 50000 and price_incr more than 10% and only pe symbols\n", "    def get_df_passing_strategy_candle_delta_gt_50000_and_price_incr_10_perc_pe(self, df):\n", "        df['prev_price'] = df.groupby('symbol')['price'].shift()\n", "        df = df[(df['price']>df['prev_price']*1.1) & ((df['price']-df['prev_price'])*df['volume']>=50000) & (df['ce_pe']=='PE')]\n", "        return df\n", "\n", "    # Candle delta > 20000 and price_incr more than 5% and only pe symbols\n", "    def get_df_passing_strategy_candle_delta_gt_20000_and_price_incr_5_perc_pe(self, df):\n", "        df['prev_price'] = df.groupby('symbol')['price'].shift()\n", "        df = df[(df['price']>df['prev_price']*1.05) & ((df['price']-df['prev_price'])*df['volume']>=20000) & (df['ce_pe']=='PE')]\n", "        return df\n", "\n", "    # Candle delta > 20000 and price_incr more than 10% and only pe symbols\n", "    def get_df_passing_strategy_candle_delta_gt_20000_and_price_incr_10_perc_pe(self, df):\n", "        df['prev_price'] = df.groupby('symbol')['price'].shift()\n", "        df = df[(df['price']>df['prev_price']*1.1) & ((df['price']-df['prev_price'])*df['volume']>=20000) & (df['ce_pe']=='PE')]\n", "        return df\n", "\n", "    # Candle delta > 10000 and price_incr more than 5% and only pe symbols\n", "    def get_df_passing_strategy_candle_delta_gt_10000_and_price_incr_5_perc_pe(self, df):\n", "        df['prev_price'] = df.groupby('symbol')['price'].shift()\n", "        df = df[(df['price']>df['prev_price']*1.05) & ((df['price']-df['prev_price'])*df['volume']>=10000) & (df['ce_pe']=='PE')]\n", "        return df\n", "\n", "    # Candle delta > 10000 and price_incr more than 10% and only pe symbols\n", "    def get_df_passing_strategy_candle_delta_gt_10000_and_price_incr_10_perc_pe(self, df):\n", "        df['prev_price'] = df.groupby('symbol')['price'].shift()\n", "        df = df[(df['price']>df['prev_price']*1.1) & ((df['price']-df['prev_price'])*df['volume']>=10000) & (df['ce_pe']=='PE')]\n", "        return df\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d = DataAnalyzer()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def run_strategy(strategy):\n", "    d = DataAnalyzer()\n", "    print(\"[[  Starting for: \", strategy, \"  ]]\")\n", "    months = [1,2]\n", "    trading_days = [d.get_trading_days(2024, month) for month in months]\n", "    print(\"trading_days: \", trading_days)\n", "    trading_days = [item for sublist in trading_days for item in sublist]\n", "    # trading_days = [t for t in trading_days if t.day <= 5 and t.month <= 3]\n", "    # trading_days = [t for t in trading_days if t.day==25 and t.month==1]\n", "    df_saveable = pd.DataFrame()\n", "    avg_success_rate = 0\n", "    total_trades = 0\n", "    for t in trading_days:\n", "        try:\n", "            df = d.get_transformed_df(day=t.day, month=t.month, year=t.year)\n", "        except:\n", "            print(\"Could not analyze: \", t)\n", "            pass\n", "        # print(\"unfiltered_df\", df)\n", "        df_pre_filtered = d.pre_filtering(df)\n", "        # print(\"prefiltered_df\", df_pre_filtered)\n", "        df_filtered = getattr(d, f'get_df_passing_strategy_{strategy}')(df_pre_filtered)\n", "        # print(df_filtered)\n", "        # print(df_filtered)\n", "        df_post_strategy = d.post_strategy(df_filtered)\n", "        # print(df_post_strategy)\n", "        df_buys = d.clean_duplicated(df_post_strategy)\n", "        # print(df_buys)\n", "        # print(df_buys)\n", "        df_pnl = d.get_pnl(df_buys, df)\n", "        df_saveable = pd.concat([df_saveable, df_pnl])\n", "        # print(df_success)\n", "        pnl = d.net_pnl(df_pnl)\n", "        if math.isnan(pnl):\n", "            pnl = 0\n", "        avg_success_rate += pnl\n", "        total_trades += len(df_buys)\n", "        print(t, \" num_trades:\", len(df_buys), \" pnl:\", round(pnl, 2))\n", "\n", "    avg_success_rate /= len(trading_days)\n", "    print(\"Net Output: stratgy: \", strategy, \"total_trades: \",total_trades, \"avg_success_rate: \", round(avg_success_rate, 2))\n", "    df_saveable['strategy'] = strategy\n", "\n", "    df_saveable.to_csv(f'strategy/trailing_march/strategy_{strategy}.csv', index=False)\n", "\n", "    # print(\"Saved csv\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["strategy_list = [\n", "    # '3_consecutive_incr_5_perc',\n", "    # 'traded_val_gt_500000',\n", "    # 'traded_val_gt_1000000',\n", "    # 'candle_delta_gt_50000_and_price_incr_5_perc_ce',\n", "    # 'candle_delta_gt_50000_and_price_incr_10_perc_ce',\n", "    # 'candle_delta_gt_50000_and_price_incr_10_perc',\n", "    # 'candle_delta_gt_50000_and_price_incr_5_perc',\n", "    # 'candle_delta_gt_50000_and_price_incr_5_perc_pe',\n", "    # 'candle_delta_gt_50000_and_price_incr_10_perc_pe',\n", "    # 'candle_delta_gt_10000_and_price_incr_5_perc_pe',\n", "    # 'candle_delta_gt_10000_and_price_incr_10_perc_pe',\n", "    # 'candle_delta_gt_20000_and_price_incr_5_perc_pe',\n", "    'candle_delta_gt_20000_and_price_incr_10_perc_pe',\n", "    # 'candle_delta_gt_100000_and_price_incr_5_perc_ce',\n", "    # 'candle_delta_gt_100000_and_price_incr_10_perc_ce',\n", "    # 'candle_delta_gt_50000_and_price_incr_1_perc',\n", "]\n", "\n", "def execute_strategy(strategy):\n", "    return run_strategy(strategy)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if __name__ == '__main__':\n", "    for strategy in strategy_list:\n", "        execute_strategy(strategy)\n"]}], "metadata": {"kernelspec": {"display_name": "algo", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 2}