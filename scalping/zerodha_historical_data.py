from filtered_instruments import FilteredInstruments
import utils
from broker import Zerodha
import psycopg2
from psycopg2 import extras
config = utils.get_config()

db_conn = psycopg2.connect(
    host=config.db_host,
    database=config.db_name,
    user=config.db_user,
    port=config.db_port
)

broker = Zerodha(
    config.zerodha_username,
    api_key=config.zerodha_api_key,
    access_token=config.zerodha_access_token
)

# x = broker.kite.historical_data(instrument_token = 11869186, from_date = '2023-11-13', to_date = '2023-11-15', interval='day')
x = broker.kite.historical_data(6401,'2022-11-21' , '2023-11-15', 'day')
print(x)


