import utils
import pdb
import yaml
import constants
from config import Config
import time
from position import Position
from kiteconnect_extras import KiteExt
import logging
import os
from broker import Zerodha
# from broker import Upstox
from strategy import RSIBuyStrategy
from strategy import RSISellStrategy
import django
import sys
from datetime import datetime
import pytz
import csv
IST = pytz.timezone('Asia/Kolkata')
from algorithm import Algo

BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(BASE_DIR)
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'proj.settings')
django.setup()
from telegram_utils import TelegramUtil
from telethon import events

logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(message)s', datefmt='%d-%b-%y %H:%M:%S')

if __name__ == "__main__":
    config = utils.get_config()
    print(config)
    # t = TelegramUtil(config)
    broker = Zerodha(
        config.zerodha_username,
        api_key=config.zerodha_api_key,
        access_token=config.zerodha_access_token
    )
    algo = Algo(config, broker)
    # instruments = algo.broker.instruments()
    # with open('../instruments.csv', newline='') as f:
    #     reader = csv.DictReader(f)
    #     instruments = list(reader)
    # print(instruments)
    import json
    instruments_list = json.load(open('fno_instruments.json'))
    instrument_token_list = [i['instrument_token'] for i in instruments_list]
    algo.run(instrument_token_list)
    # instruments_fut = [i for i in instruments if i['exchange'] == 'NFO' and i['instrument_type'] == 'FUT']

    # instrument_names = sorted(list(set([i['name'] for i in instruments_fut if 'NIFTY' not in i['name']])))
    # print(instrument_names, len(instrument_names))
    # import json
    # instruments2 = [i for i in instruments if
    #                 i['exchange'] == 'NSE'
    #                 and i['instrument_type'] == 'EQ'
    #                 and i['tradingsymbol'] in instrument_names
    #                 ]
    # with open("fno_instruments.json", "w") as outfile:
    #     json.dump(instruments2, outfile)
    # instrument_names2 = sorted(list(set([i['tradingsymbol'] for i in instruments2])))
    #
    # print(instrument_names2, len(instrument_names2))
