from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
import os

def simple_hover_test():
    """Simple test to find hover tooltips with date information"""
    
    # Setup Chrome options
    options = webdriver.ChromeOptions()
    mobile_emulation = {
        "deviceMetrics": {"width": 375, "height": 812, "pixelRatio": 3.0},
        "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"
    }
    options.add_experimental_option("mobileEmulation", mobile_emulation)
    options.add_argument("--window-size=375,812")
    
    user_data_dir = os.path.join(os.path.expanduser("~"), "telegram_selenium_profile")
    options.add_argument(f"--user-data-dir={user_data_dir}")
    
    driver = webdriver.Chrome(options=options)

    try:
        # Navigate to Telegram channel
        driver.get("https://web.telegram.org/a/#-1002075758731")
        time.sleep(5)
        
        print("Looking for elements with hover tooltips...")
        
        # Simple script to hover over everything and look for tooltips
        hover_script = """
        // Get all elements on the page
        const allElements = document.querySelectorAll('*');
        let foundTooltips = [];
        
        // Function to trigger hover and check for tooltips
        function checkElement(element) {
            // Skip if element is not visible
            const rect = element.getBoundingClientRect();
            if (rect.width === 0 || rect.height === 0) return;
            
            // Get initial title
            const initialTitle = element.getAttribute('title') || '';
            
            // Trigger hover
            const event = new MouseEvent('mouseover', {
                view: window,
                bubbles: true,
                cancelable: true,
                clientX: rect.left + rect.width / 2,
                clientY: rect.top + rect.height / 2
            });
            element.dispatchEvent(event);
            
            // Check for title after hover
            setTimeout(() => {
                const afterTitle = element.getAttribute('title') || '';
                const text = element.innerText || element.textContent || '';
                
                // Look for date/time patterns
                if (afterTitle.includes('August') || afterTitle.includes('AM') || afterTitle.includes('PM') ||
                    text.includes('11:') || text.includes('12:') || afterTitle.includes('2025')) {
                    foundTooltips.push({
                        tagName: element.tagName,
                        className: element.className || '',
                        text: text.substring(0, 100),
                        initialTitle: initialTitle,
                        afterTitle: afterTitle,
                        hasDate: afterTitle.includes('August 2025') || afterTitle.includes('13 August')
                    });
                }
            }, 100);
        }
        
        // Check elements that might contain messages
        const messageElements = [];
        
        // Look for elements with trading alerts
        allElements.forEach(element => {
            const text = element.innerText || element.textContent || '';
            if (text.includes('Alert:') && text.includes('@')) {
                messageElements.push(element);
            }
        });
        
        console.log('Found', messageElements.length, 'message elements');
        
        // Check each message element and its children
        messageElements.forEach(msgElement => {
            checkElement(msgElement);
            
            // Also check all children
            const children = msgElement.querySelectorAll('*');
            children.forEach(child => {
                checkElement(child);
            });
        });
        
        // Wait for all hover effects
        setTimeout(() => {
            return foundTooltips;
        }, 2000);
        
        return foundTooltips;
        """
        
        # Execute the hover test
        print("Executing hover test...")
        tooltips = driver.execute_script(hover_script)
        time.sleep(3)  # Wait for hover effects
        
        # Get final results
        final_check = """
        const allElements = document.querySelectorAll('*');
        let results = [];
        
        allElements.forEach(element => {
            const title = element.getAttribute('title') || '';
            const text = element.innerText || element.textContent || '';
            
            if (title.includes('August') || title.includes('2025') || title.includes('AM') || title.includes('PM')) {
                results.push({
                    tagName: element.tagName,
                    className: element.className || '',
                    text: text.substring(0, 100),
                    title: title,
                    hasTargetDate: title.includes('13 August 2025')
                });
            }
        });
        
        return results;
        """
        
        final_results = driver.execute_script(final_check)
        
        print(f"\n=== FOUND {len(final_results)} ELEMENTS WITH DATE/TIME INFO ===")
        for result in final_results:
            print(f"Tag: {result['tagName']}")
            print(f"Class: {result['className']}")
            print(f"Text: {result['text']}")
            print(f"Title: {result['title']}")
            print(f"Has target date: {result['hasTargetDate']}")
            print("-" * 50)
        
        # Try a different approach - look for specific time elements
        print("\n=== LOOKING FOR TIME ELEMENTS ===")
        time_search = """
        // Look for elements that might show time
        const timeSelectors = [
            '[class*="time"]',
            '[class*="date"]', 
            '[class*="timestamp"]',
            '.time',
            '.date',
            '.timestamp'
        ];
        
        let timeElements = [];
        timeSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(el => {
                if (!timeElements.includes(el)) {
                    timeElements.push(el);
                }
            });
        });
        
        return timeElements.map(el => ({
            tagName: el.tagName,
            className: el.className || '',
            text: el.innerText || el.textContent || '',
            title: el.getAttribute('title') || '',
            outerHTML: el.outerHTML.substring(0, 200)
        }));
        """
        
        time_elements = driver.execute_script(time_search)
        
        print(f"Found {len(time_elements)} time-related elements:")
        for elem in time_elements:
            print(f"Tag: {elem['tagName']}, Class: {elem['className']}")
            print(f"Text: {elem['text']}")
            print(f"Title: {elem['title']}")
            print(f"HTML: {elem['outerHTML']}")
            print("-" * 30)
        
    finally:
        input("Press Enter to close browser...")
        driver.quit()

if __name__ == "__main__":
    simple_hover_test()
