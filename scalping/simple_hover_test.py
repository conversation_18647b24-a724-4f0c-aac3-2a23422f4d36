from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
import time
import os

def simple_hover_test():
    """Simple test to find hover tooltips with date information"""
    
    # Setup Chrome options
    options = webdriver.ChromeOptions()
    mobile_emulation = {
        "deviceMetrics": {"width": 375, "height": 812, "pixelRatio": 3.0},
        "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"
    }
    options.add_experimental_option("mobileEmulation", mobile_emulation)
    options.add_argument("--window-size=375,812")
    
    user_data_dir = os.path.join(os.path.expanduser("~"), "telegram_selenium_profile")
    options.add_argument(f"--user-data-dir={user_data_dir}")
    
    driver = webdriver.Chrome(options=options)

    try:
        # Navigate to Telegram channel
        driver.get("https://web.telegram.org/a/#-1002075758731")
        time.sleep(5)
        
        print("Looking for timestamp elements to hover over...")

        # First, find message bubbles with trading alerts
        find_messages_script = """
        const allElements = document.querySelectorAll('*');
        const messageBubbles = [];

        allElements.forEach(element => {
            const text = element.innerText || element.textContent || '';
            if (text.includes('Alert:') && text.includes('@') && text.length < 200) {
                messageBubbles.push({
                    element: element,
                    text: text.substring(0, 100),
                    tagName: element.tagName,
                    className: element.className || ''
                });
            }
        });

        console.log('Found', messageBubbles.length, 'message bubbles');
        return messageBubbles.map(msg => ({
            text: msg.text,
            tagName: msg.tagName,
            className: msg.className
        }));
        """

        messages = driver.execute_script(find_messages_script)
        print(f"Found {len(messages)} message bubbles:")
        for i, msg in enumerate(messages[-5:]):  # Show last 5
            print(f"  {i+1}. {msg['tagName']}.{msg['className']}: {msg['text']}")

        # Now find timestamp elements specifically
        find_timestamps_script = """
        // Find all potential timestamp elements
        const timestampSelectors = [
            '[class*="time"]',
            '[class*="date"]',
            '[class*="timestamp"]',
            '.time',
            '.date',
            'time',
            '[title*=":"]',
            '[title*="AM"]',
            '[title*="PM"]'
        ];

        let timestampElements = [];
        timestampSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(el => {
                if (!timestampElements.includes(el)) {
                    timestampElements.push(el);
                }
            });
        });

        return timestampElements.map(el => ({
            tagName: el.tagName,
            className: el.className || '',
            text: el.innerText || el.textContent || '',
            title: el.getAttribute('title') || '',
            outerHTML: el.outerHTML.substring(0, 150)
        }));
        """

        timestamps = driver.execute_script(find_timestamps_script)
        print(f"\nFound {len(timestamps)} timestamp elements:")
        for i, ts in enumerate(timestamps):
            print(f"  {i+1}. {ts['tagName']}.{ts['className']}")
            print(f"     Text: {ts['text']}")
            print(f"     Title: {ts['title']}")
            print(f"     HTML: {ts['outerHTML']}")
            print("-" * 30)
        
        # Now test hovering over timestamp elements to reveal date tooltips
        print("\n=== TESTING TIMESTAMP HOVER ===")

        hover_test_script = """
        // Find timestamp elements again
        const timestampSelectors = [
            '[class*="time"]',
            '[class*="date"]',
            '[class*="timestamp"]',
            '.time',
            '.date',
            'time'
        ];

        let timestampElements = [];
        timestampSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(el => {
                if (!timestampElements.includes(el)) {
                    timestampElements.push(el);
                }
            });
        });

        console.log('Hovering over', timestampElements.length, 'timestamp elements');

        // Hover over each timestamp element
        timestampElements.forEach((timestampEl, index) => {
            timestampEl.scrollIntoView({ block: 'center' });

            const rect = timestampEl.getBoundingClientRect();
            if (rect.width > 0 && rect.height > 0) {
                // Trigger multiple hover events
                ['mouseenter', 'mouseover', 'mousemove'].forEach(eventType => {
                    const event = new MouseEvent(eventType, {
                        view: window,
                        bubbles: true,
                        cancelable: true,
                        clientX: rect.left + rect.width / 2,
                        clientY: rect.top + rect.height / 2
                    });
                    timestampEl.dispatchEvent(event);
                });

                console.log('Hovered over timestamp', index + 1);
            }
        });

        return timestampElements.length;
        """

        timestamp_count = driver.execute_script(hover_test_script)
        print(f"Hovered over {timestamp_count} timestamp elements")

        # Wait for hover effects to show tooltips
        time.sleep(2)

        # Check for any new tooltips that appeared
        check_tooltips_script = """
        const allElements = document.querySelectorAll('*');
        let tooltips = [];

        allElements.forEach(element => {
            const title = element.getAttribute('title') || '';
            if (title && (title.includes('August') || title.includes('2025') || title.includes('AM') || title.includes('PM'))) {
                tooltips.push({
                    tagName: element.tagName,
                    className: element.className || '',
                    text: element.innerText || element.textContent || '',
                    title: title,
                    hasTargetDate: title.includes('13 August 2025')
                });
            }
        });

        return tooltips;
        """

        tooltips = driver.execute_script(check_tooltips_script)

        print(f"\n=== FOUND {len(tooltips)} ELEMENTS WITH DATE/TIME TOOLTIPS ===")
        for tooltip in tooltips:
            print(f"Tag: {tooltip['tagName']}")
            print(f"Class: {tooltip['className']}")
            print(f"Text: {tooltip['text']}")
            print(f"Title: {tooltip['title']}")
            print(f"Has target date (13 August 2025): {tooltip['hasTargetDate']}")
            print("-" * 50)
        
        # Try a different approach - look for specific time elements
        print("\n=== LOOKING FOR TIME ELEMENTS ===")
        time_search = """
        // Look for elements that might show time
        const timeSelectors = [
            '[class*="time"]',
            '[class*="date"]', 
            '[class*="timestamp"]',
            '.time',
            '.date',
            '.timestamp'
        ];
        
        let timeElements = [];
        timeSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(el => {
                if (!timeElements.includes(el)) {
                    timeElements.push(el);
                }
            });
        });
        
        return timeElements.map(el => ({
            tagName: el.tagName,
            className: el.className || '',
            text: el.innerText || el.textContent || '',
            title: el.getAttribute('title') || '',
            outerHTML: el.outerHTML.substring(0, 200)
        }));
        """
        
        time_elements = driver.execute_script(time_search)
        
        print(f"Found {len(time_elements)} time-related elements:")
        for elem in time_elements:
            print(f"Tag: {elem['tagName']}, Class: {elem['className']}")
            print(f"Text: {elem['text']}")
            print(f"Title: {elem['title']}")
            print(f"HTML: {elem['outerHTML']}")
            print("-" * 30)
        
    finally:
        input("Press Enter to close browser...")
        driver.quit()

if __name__ == "__main__":
    simple_hover_test()
