from datetime import date, datetime, timedelta
import calendar
import math
import os
import yaml
from config import Config


def next_expiry_prefix(sub_prefix):
    month_map = {10: 'O', 11: 'N', 12: 'D'}
    next_thursday_date = date.today() + timedelta((calendar.THURSDAY -
                                                   date.today().weekday()) % 7)
    cal_month = next_thursday_date.strftime("%b").upper()
    month = calendar.monthcalendar(next_thursday_date.year, next_thursday_date.month)
    last_thursday = max(month[-1][calendar.THURSDAY], month[-2][calendar.THURSDAY])
    # print(last_thursday)
    next_thursday = next_thursday_date.day
    tyear = str(next_thursday_date.year)[2:]
    tmonth = month_map[next_thursday_date.month] if next_thursday_date.month >= 10 else str(
        next_thursday_date.month)
    tdate = "%02d" % next_thursday
    if (next_thursday == last_thursday) or (sub_prefix not in ['NIFTY', 'BANKNIFTY']):
        tmonth = cal_month
        tdate = ""
    return sub_prefix+tyear+tmonth+tdate


def get_nifty_banknifty_prefix(strike_price):
    if strike_price > 28000:
        return 'BANKNIFTY'
    else:
        return 'NIFTY'


def get_strike_name(symbol):
    return symbol[-7:]


def roundup(x):
    return str(int(math.ceil(x / 100.0) * 100-200.0))


def rounddown(x):
    return str(int(math.floor(x / 100.0) * 100.0+200.0))


def get_config():
    config_data = {}
    with open('config.yml') as f:
        config_data = yaml.load(f, Loader=yaml.CLoader)
    if os.path.isfile('config_local.yml'):
        with open('config_local.yml') as f:
            local_config_data = yaml.load(f, Loader=yaml.CLoader)
            for k in local_config_data:
                config_data[k] = local_config_data[k]
    return Config(config_data)


def get_fno_lot_data():
    fno_lot_data = {}
    with open('fno_lot_data.yml') as f:
        fno_lot_data = yaml.load(f, Loader=yaml.CLoader)
    return fno_lot_data
