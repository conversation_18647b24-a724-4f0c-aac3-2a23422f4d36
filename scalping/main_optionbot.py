# import necessary libraries
import logging
from telegram.ext import <PERSON><PERSON><PERSON>, CommandHandler, MessageHandler, Filters, CallbackQueryHandler, CallbackContext
from telegram import InlineKeyboardButton, InlineKeyboardMarkup, KeyboardButton, Update
# set up logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# input our telegram bot unique api token
TOKEN = '**********************************************'

def start(update, context):
    # send a message once the bot is started/start command is ran
    print("args: ----> ", context.args)
    keyboard = [
        [
            InlineKeyboardButton("Option 1", callback_data="1"),
            InlineKeyboardButton("Option 2", callback_data="2"),
        ],
        [InlineKeyboardButton("Option 3", callback_data="3")],
    ]

    reply_markup = InlineKeyboardMarkup(keyboard)

    update.message.reply_text("Please choose:", reply_markup=reply_markup)
    # update.message.reply_text('Testing 123')

def buy(update, context):
    print("args: ----> ", context.args)
    # send a message once the command /hello is keyed
    update.message.reply_text('Buy order placed \U0001F600')


def positions(update, context):
    # send a message once the bot is started/start command is ran
    print("args: ----> ", context.args)
    keyboard = [
        [InlineKeyboardButton("VEDL 300 CE. BuyPrice: 250, Target: 500, SL: 200. Status: Ongoing", callback_data="1")],
        [InlineKeyboardButton("RELIANCE 2500 CE. BuyPrice: 50, Target: 65, SL: 40. Status: SL Hit.", callback_data="2")],
        [InlineKeyboardButton("TECHM 1200 CE. BuyPrice: 10, Target: 20, SL: 2, Status: Target Hit", callback_data="3")],
    ]

    reply_markup = InlineKeyboardMarkup(keyboard)

    update.message.reply_text(text="Positions:", reply_markup=reply_markup)

    # update.message.reply_text('Testing 123')

# for error debugging
def error(update, context):
    logger.warning('Update "%s" caused error "%s"', update, context.error)

def messageHandler(update, context):
    logger.warning('Update messageHandler: ')
    if 'VEDL' in update.message.text:
        update.message.reply_text(text="Selected VEDL")


def buttonHandler(update, context):
    logger.warning('Update buttonHandler: ')
    query = update.callback_query
    print("data: ", query.__dict__)

# to start the bot
def main():
    # setup updating together with our telegram api token
    updater = Updater(TOKEN, use_context=True)
# get the dispatcher to register handlers
    dp = updater.dispatcher
# add command handlers for different command
    dp.add_handler(CommandHandler("start", start, pass_args=True))
    dp.add_handler(CommandHandler("buy", buy, pass_args=True))
    dp.add_handler(CommandHandler("positions", positions, pass_args=True))
    dp.add_handler(CallbackQueryHandler(buttonHandler))
    dp.add_handler(MessageHandler(Filters.text, messageHandler))

# error logging
    dp.add_error_handler(error)
# start the bot
    updater.start_polling()
# set the bot to run until you force it to stop
    updater.idle()
if __name__ == '__main__':
    main()