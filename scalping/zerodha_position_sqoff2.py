import logging
import utils
from broker import <PERSON><PERSON>
from croniter import croniter
from datetime import datetime
import time
from filtered_instruments import FilteredInstruments

logging.basicConfig(level=logging.INFO,
                    format="%(asctime)s %(levelname)s: %(message)s",
                    datefmt="%Y-%m-%d %H:%M:%S")

class ZerodhaPositionSqoff:
    def __init__(self):
        self.config = utils.get_config()
        self.broker = Zerodha(
            self.config.zerodha_username,
            api_key=self.config.zerodha_api_key,
            access_token=self.config.zerodha_access_token
        )
        self.filtered_instruments = FilteredInstruments(self.broker).df_raw

    def on_ticks(self, ws, ticks):
        logging.info("ticks: {}".format(ticks))

    def on_connect(self, ws, response):
        # Callback on successful connect.
        # Subscribe to a list of instrument_tokens (BANKNIFTY and NIFTY here).
        logging.info("ws_connected")
        ws.subscribe(ws.instrument_token_list)
        # Set to tick in `ltp` mode.
        ws.set_mode(ws.MODE_FULL, ws.instrument_token_list)
        # ws.set_mode(ws.MODE_LTP, ws.instrument_token_list)

    def on_error(self, ws, code, reason):
        # On connection close stop the main loop
        # Reconnection will not happen after executing `ws.stop()`
        logging.info("{}{}".format(code, reason))

    def on_close(self, ws, code, reason):
        # On connection close stop the main loop
        # Reconnection will not happen after executing `ws.stop()`
        logging.info("{}{}".format(code, reason))
        # ws.stop()

    def on_order_update(self, ws, data):
        # Triggered when there is an order update for the connected user.
        logging.info("on_order_update: {}".format(data))
        # ws.stop()

    def run(self, instrument_token_list=None):
        kws = self.broker.ticker()
        # Assign the callbacks.
        kws.instrument_token_list = instrument_token_list
        kws.on_ticks = self.on_ticks
        kws.on_order_update = self.on_order_update
        kws.on_connect = self.on_connect
        kws.on_close = self.on_close
        kws.on_error = self.on_error
        kws.connect(threaded=False)


    def task(self):
        positions = self.broker.positions()
        open_positions = [p for p in positions if p['quantity']!=0]
        symbols_with_open_orders = [o['tradingsymbol'] for o in self.broker.orders() if o['status'] == 'OPEN']
        for p in open_positions:
            if p['tradingsymbol'] in symbols_with_open_orders:
                continue
            else:
                print("placing_sell_order_for: ", p['tradingsymbol'])
                script_tick_size = self.filtered_instruments.loc[self.filtered_instruments['tradingsymbol'] == p['tradingsymbol'], 'tick_size'].values[0]
                price = int(p['buy_price']*1.1/script_tick_size)*script_tick_size
                self.broker.place_order(tradingsymbol=p['tradingsymbol'], transaction_type='SELL', quantity=[p['quantity']],
            product='NRML', order_type='LIMIT', price=price)

# schedule = "* * * * *"
# cron = croniter(schedule, datetime.now())

z = ZerodhaPositionSqoff()
# positions = z.broker.positions()
# print(positions)

z.run()

# while True:
#     next_execution = cron.get_next(datetime)
#     print(next_execution)
#     time.sleep((next_execution - datetime.now()).total_seconds()+10)
#     z.task()
