import pika
import json
from broker import Zerodha
import utils
import logging
import psycopg2
from filtered_instruments import FilteredInstruments
from telegram_utils import TelegramUtil
from datetime import datetime
import time
import asyncio
import traceback
from broker_singleton import BrokerSingleton
import sys
import prettytable as pt

logging.basicConfig(level=logging.INFO,
                    format="%(asctime)s %(levelname)s: %(message)s",
                    datefmt="%Y-%m-%d %H:%M:%S")

class DailyTradeUpdater:
    def __init__(self):
        self.config = utils.get_config()
        self.broker = BrokerSingleton()
        self.filtered_instruments = FilteredInstruments(self.broker).df_raw
        self.db_conn = psycopg2.connect(
            host=self.config.db_host,
            database=self.config.db_name,
            user=self.config.db_user,
            port=self.config.db_port
        )
        self.telegram_client = TelegramUtil(config=self.config, client=True, bot=False, session_name='day_report')
        self.telegram_chat_id = -1002075758731
        self.open_trades_query = f"""
            SELECT * FROM daily_option_trades WHERE time > CURRENT_DATE
        """
        self.update_gain_query = f"""
            UPDATE daily_option_trades SET current_gain = %s WHERE time > CURRENT_DATE AND symbol = %s
        """


if __name__ == "__main__":
    d = DailyTradeUpdater()
    table = pt.PrettyTable(['Symbol', 'Gain'])
    positions = []
    todays_msgs = d.telegram_client.read_todays_messages(d.telegram_chat_id)
    todays_msgs = [m.message for m in todays_msgs if m.message]
    if not todays_msgs:
        sys.exit()
    with d.db_conn.cursor() as cur:
        cur.execute(d.open_trades_query)
        columns = [column[0] for column in cur.description]
        for row in cur.fetchall():
            positions.append(dict(zip(columns, row)))
    positions = [p for p in positions if any(p['symbol'] in t for t in todays_msgs)]
    if not positions:
        sys.exit()
    quotes = d.broker.get_quote(['NFO:'+p['symbol'] for p in positions])
    quotes = {key.replace('NFO:', ''): value for key, value in quotes.items()}
    total_gain = 0
    for p in positions:
        symbol = p['symbol']
        if p['current_gain']:
            current_gain = int(p['current_gain']*100)
        else:
            current_price = quotes[symbol]['last_price']
            current_gain = int(round((current_price - p['buy_price']) / p['buy_price'], 2)*100)
        total_gain = total_gain + current_gain
        table.add_row([p['symbol'], f"{current_gain}%"])
    avg_gain = int(total_gain/len(positions))
    title = f"Todays trades. Overall gain: {avg_gain}%"
    msg = "<b>{}</b><pre>{}</pre>".format(title, str(table))
    loop = asyncio.get_event_loop()
    loop.run_until_complete(d.telegram_client.send_message(chatid=d.telegram_chat_id, msg=msg))
