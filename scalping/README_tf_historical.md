# OHLC Timeframe Historical Data Population

This script populates OHLC tables (`ohlc_5min`, `ohlc_15min`, `ohlc_1hour`, `ohlc_daily`) with historical OHLC data for all NSE F&O stocks using the Zerodha Kite historical API.

## 📋 Features

- ✅ **Multiple timeframes**: 5min, 15min, 1hour, daily
- ✅ **Gets NSE F&O stocks** from `FilteredInstruments.nse_stocks`
- ✅ **Uses Zerodha Kite historical API** for reliable data
- ✅ **Configurable date ranges** (days back or custom dates)
- ✅ **Rate limiting** (3 requests/second as per Zerodha limits)
- ✅ **Skip existing data** option to avoid duplicates
- ✅ **Command line arguments** for easy automation
- ✅ **Comprehensive error handling** and logging
- ✅ **Data verification** after population
- ✅ **Test mode** for safe testing

## 🚀 Usage

### 1. Basic Usage (Days Back)
```bash
python populate_tf_historical.py daily 30        # Daily data for last 30 days
python populate_tf_historical.py 5min 7          # 5min data for last 7 days
python populate_tf_historical.py 15min 14        # 15min data for last 14 days
python populate_tf_historical.py 1hour 30        # 1hour data for last 30 days
```

### 2. Custom Date Range
```bash
python populate_tf_historical.py daily custom 2024-01-01 2024-01-31
python populate_tf_historical.py 5min custom 2024-01-15 2024-01-16
```

### 3. Force Overwrite Existing Data
```bash
python populate_tf_historical.py daily custom 2024-01-01 2024-01-31 force
```

### 4. Test Mode (5 stocks only)
```bash
python populate_tf_historical.py test daily 7    # Test daily data for 7 days
python populate_tf_historical.py test 5min 1     # Test 5min data for 1 day
```

## 📊 Data Schema

The script populates the `ohlc_daily` table with:

| Column | Type | Description |
|--------|------|-------------|
| `time` | timestamp with time zone | Trading date |
| `symbol` | text | Stock symbol (e.g., 'RELIANCE') |
| `price` | double precision | Closing price |
| `volume` | integer | Total volume traded |
| `oi` | integer | Open Interest (if available) |

## ⚡ Performance

- **Rate Limiting**: 400ms delay between API calls (2.5 requests/second)
- **Expected Time**: ~200 stocks × 0.4s = ~80 seconds for all stocks
- **API Limits**: Respects Zerodha's 3 requests/second limit
- **Memory Efficient**: Processes one stock at a time

## 🔧 Configuration

The script uses your existing Zerodha configuration from `config.yml`:
- `zerodha_api_key`
- `zerodha_access_token`
- Database connection settings

## 📝 Logging

The script provides detailed logging:
- Progress updates every 10 stocks
- Success/failure status for each stock
- Summary statistics at the end
- Error details for debugging

## 🛡️ Error Handling

- **API Errors**: Continues with next stock if one fails
- **Rate Limiting**: Automatic delays between requests
- **Database Errors**: Rollback on insertion failures
- **Network Issues**: Retry logic built into Kite API

## 📈 Example Output

```
2024-01-15 10:30:00 INFO: Starting daily OHLC population from Zerodha historical API...
2024-01-15 10:30:01 INFO: Found 187 NSE F&O stocks
2024-01-15 10:30:01 INFO: Populating daily OHLC data from 2023-12-16 to 2024-01-15
2024-01-15 10:30:01 INFO: Processing 187 stocks...
2024-01-15 10:30:02 INFO: Processing stock 1/187: RELIANCE
2024-01-15 10:30:02 INFO: Fetched 21 daily records for RELIANCE
2024-01-15 10:30:02 INFO: Inserted 21 daily records
2024-01-15 10:30:02 INFO: ✅ Successfully processed RELIANCE
...
2024-01-15 10:32:15 INFO: ==================================================
2024-01-15 10:32:15 INFO: POPULATION SUMMARY
2024-01-15 10:32:15 INFO: ==================================================
2024-01-15 10:32:15 INFO: Total stocks processed: 187
2024-01-15 10:32:15 INFO: Successful: 185
2024-01-15 10:32:15 INFO: Failed: 2
2024-01-15 10:32:15 INFO: Total records inserted: 3885
2024-01-15 10:32:15 INFO: Date range: 2023-12-16 to 2024-01-15
```

## 🔍 Verification

After population, the script automatically verifies:
- Total records inserted
- Number of unique symbols
- Date range coverage
- Sample of recent data

## ⚠️ Important Notes

1. **API Limits**: Zerodha allows 3 requests/second. The script respects this limit.
2. **Market Hours**: Can be run anytime - fetches historical data.
3. **Data Quality**: Uses official Zerodha data, same as your trading platform.
4. **Incremental Updates**: Use `skip_existing=True` to avoid re-downloading existing data.
5. **Storage**: ~200 stocks × 30 days = ~6000 records (minimal storage impact).

## 🐛 Troubleshooting

### Common Issues:

1. **"No data received"**: Stock might be newly listed or suspended
2. **API Rate Limit**: Script automatically handles this with delays
3. **Token Expired**: Update your `zerodha_access_token` in config.yml
4. **Network Issues**: Script will continue with next stock

### Debug Mode:
```bash
# Enable debug logging
export PYTHONPATH=.
python -c "
import logging
logging.basicConfig(level=logging.DEBUG)
exec(open('populate_daily_historical.py').read())
"
```

## 🔄 Integration

This script complements your existing OHLC system:
- `ohlc_1min`: Real-time minute data
- `ohlc_5min`, `ohlc_15min`, `ohlc_1hour`: Aggregated from 1min data
- `ohlc_daily`: Historical daily data from this script

## 📅 Scheduling

For regular updates, you can schedule this script:

```bash
# Daily at 6 PM (after market close)
0 18 * * 1-5 cd /path/to/scalping && python populate_daily_historical.py
```

This ensures your daily data is always up-to-date with the latest trading day.
