import psycopg2
import config
class DB:
    def __init__(self, db_host, db_name, db_user, db_pass):
        self.conn = psycopg2.connect(
            host=db_host,
            database=db_name,
            user=db_user,
            password=db_pass)

    def insert_into(self, table_name: str, row):
        cur = self.conn.cursor()
        query = "insert into {} (screener_id, stock, created_on) values ('{}', '{}', '{}');".format(table_name, row['screener_id'], row['stock'], row['created_on'])
        # print(query)
        try:
            cur.execute(query)
        except Exception as e:
            cur.execute("ROLLBACK")
            raise e
        cur.close()
        self.conn.commit()

    def increment_count(self, table_name: str, where):
        cur = self.conn.cursor()
        query = "update {} set count = count+1 where screener_id='{}' and stock='{}' and created_on='{}';".format(table_name, where['screener_id'], where['stock'], where['created_on'])
        # print(query)
        try:
            cur.execute(query)
        except Exception as e:
            cur.execute("ROLLBACK")
            raise e
        cur.close()
        self.conn.commit()