import pika
import json
from broker import Zerodha
import utils
import logging
import psycopg2
from filtered_instruments import FilteredInstruments
from telegram_utils import TelegramUtil
from datetime import datetime
import time
import asyncio
import traceback
from broker_singleton import BrokerSing<PERSON>

logging.basicConfig(level=logging.INFO,
                    format="%(asctime)s %(levelname)s: %(message)s",
                    datefmt="%Y-%m-%d %H:%M:%S")

class DailyTradeUpdater:
    def __init__(self):
        self.config = utils.get_config()
        self.broker = BrokerSingleton()
        self.filtered_instruments = FilteredInstruments(self.broker).df_raw
        self.db_conn = psycopg2.connect(
            host=self.config.db_host,
            database=self.config.db_name,
            user=self.config.db_user,
            port=self.config.db_port
        )
        self.telegram_client = TelegramUtil(config=self.config, client=True, bot=False, session_name='daily_trade_updater')
        self.telegram_chat_id = -1002075758731
        self.open_trades_query = f"""
            SELECT * FROM daily_option_trades WHERE time > CURRENT_DATE
        """
        self.update_gain_query = f"""
            UPDATE daily_option_trades SET current_gain = %s WHERE time > CURRENT_DATE AND symbol = %s
        """

    def get_telegram_msg_with_sub_string(self, msgs, substr):
        return next((m for m in msgs if substr in m.message), None)

    def task(self):
        positions = []
        todays_msgs = self.telegram_client.read_todays_messages(self.telegram_chat_id)
        if not todays_msgs:
            return

        with self.db_conn.cursor() as cur:
            cur.execute(self.open_trades_query)
            columns = [column[0] for column in cur.description]
            for row in cur.fetchall():
                positions.append(dict(zip(columns, row)))

        if not positions:
            return
        quotes = self.broker.get_quote(['NFO:'+p['symbol'] for p in positions])
        quotes = {key.replace('NFO:', ''): value for key, value in quotes.items()}

        for p in positions:
            symbol = p['symbol']
            buy_price = p['buy_price']
            current_price = quotes[symbol]['last_price']

            prev_gain = round(p.get('current_gain', 0) or 0, 2)
            current_gain = round((current_price - buy_price) / buy_price, 2)
            print(datetime.now(), 'symbol: ', symbol, ', buy_price: ', buy_price, ', current_price: ', current_price, ', prev_gain: ', prev_gain, ', current_gain: ', current_gain)

            # SL already got hit, No need to keep checking this instrument
            if prev_gain<=-0.2:
                continue
            # Price moved up by 5% from last price
            elif current_gain-prev_gain>=0.05:
                with self.db_conn.cursor() as cur:
                    data = (current_gain, symbol)
                    cur.execute(self.update_gain_query, data)
                    self.db_conn.commit()
                msg = next((m for m in todays_msgs if symbol in m.message), None)
                if msg:
                    loop = asyncio.get_event_loop()
                    loop.run_until_complete(self.telegram_client.send_message(chatid=self.telegram_chat_id, msg = f"{current_price}. Up: {int(int(current_gain*100) / 5) * 5}%", reply_to=msg.id))
            # We already saw a 10% increase, now even if SL hits we need to ignore that
            elif prev_gain>=0.1:
                continue
            # Price moved down by 20% from last price without going up by 10%, this is a SL hit
            elif current_gain<=-0.2:
                with self.db_conn.cursor() as cur:
                    data = (current_gain, symbol)
                    cur.execute(self.update_gain_query, data)
                    self.db_conn.commit()
                msg = next((m for m in todays_msgs if symbol in m.message), None)
                if msg:
                    loop = asyncio.get_event_loop()
                    loop.run_until_complete(self.telegram_client.send_message(chatid=self.telegram_chat_id, msg = "SL hit", reply_to=msg.id))


    def start(self):
        while True:
            self.task()
            time.sleep(60)

    def update_trades(self):
        pass

if __name__ == "__main__":
    d = DailyTradeUpdater()
    d.start()


