from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.common.exceptions import NoSuchElementException, ElementNotInteractableException, InvalidCookieDomainException
from selenium.webdriver.common.keys import Keys
import time
import browser_cookie3
import os
import requests

username_str = 'DR6733'
password_str = 'tr0ubleM@ker'
pin_str = '100291'

options = webdriver.ChromeOptions()
options.add_argument('user-data-dir=/Users/<USER>/Library/Application Support/Google/Chrome/')
options.add_experimental_option("detach", True)
options.add_argument('disable-infobars')
zerodha_console_cookies = browser_cookie3.chrome(domain_name='console.zerodha.com')
zerodha_kite_cookies = browser_cookie3.chrome(domain_name='console.zerodha.com')

url = "https://console.zerodha.com/api/user_profile/segment/deactivate"
payload = "{\"segments\":[\"NSE_FO\"]}"

response = requests.request("POST", url, data=payload, cookies=zerodha_console_cookies)
print(response.text)
