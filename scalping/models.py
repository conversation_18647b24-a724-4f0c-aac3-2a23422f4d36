from django.db import models

# Create your models here.
from django.db import models
from enum import Enum


class OrderStatus(Enum):
    PENDING = 'pending'
    FAILED = 'failed'
    SUCCESSFUL = 'successful'
    COMPLETED = 'completed'


class Order(models.Model):
    symbol = models.CharField(max_length=50)
    quantity = models.IntegerField()
    status = models.CharField(max_length=10, choices=[(i, i.value) for i in OrderStatus])
    buy_price = models.DecimalField(max_digits=8, decimal_places=2)
    sell_price = models.DecimalField(max_digits=8, decimal_places=2, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


class Instrument(models.Model):
    symbol = models.CharField(max_length=50)
    instrument_token = models.IntegerField()


class Tick(models.Model):
    symbol = models.CharField(max_length=50)
    cmp = models.DecimalField(max_digits=8, decimal_places=2)
    created_at = models.DateTimeField(auto_now_add=True)

