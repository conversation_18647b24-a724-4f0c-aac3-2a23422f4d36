import utils
from broker import <PERSON><PERSON>
from croniter import croniter
from datetime import datetime
import time
from filtered_instruments import FilteredInstruments
from telegram_utils import TelegramUtil
import traceback
import signal
import sys
import asyncio
from broker_singleton import Broker<PERSON>ing<PERSON>

signal.signal(signal.SIGINT, lambda x, y: sys.exit(0))

class ZerodhaPositionSqoff:
    def __init__(self):
        self.config = utils.get_config()
        self.broker = BrokerSingleton()

        f = FilteredInstruments(self.broker)
        self.filtered_instruments = f.df_raw
        self.telegram_bot = TelegramUtil(config=self.config, client=False, bot=True)
        self.telegram_chat_id = -1002075758731

    def task(self):
        positions = self.broker.positions()
        open_positions = [p for p in positions if p['quantity']>0 and p['product']=='MIS']
        all_orders = self.broker.orders()
        open_orders = [o for o in all_orders if o['status'] == 'OPEN']
        open_buy_orders = [o for o in open_orders if o['transaction_type']=='BUY']

        # Cancel an order if couldn't be filled for more than a minute
        for o in open_buy_orders:
            order_timestamp = o['order_timestamp']
            time_diff = datetime.now() - order_timestamp
            if time_diff.total_seconds() > 60:
                self.broker.cancel_order(order_id=o['order_id'])
                open_orders.remove(o)
                print("order_cancelled: ", o['tradingsymbol'])

        for p in open_positions:
            if not (p['tradingsymbol'].endswith('CE') or p['tradingsymbol'].endswith('PE')):
                continue
            script_tick_size = self.filtered_instruments.loc[self.filtered_instruments['tradingsymbol'] == p['tradingsymbol'], 'tick_size'].values[0]
            buy_price = p['buy_price']
            position_qty = p['quantity']
            target_price = buy_price*1.1
            
            # sl_price = int(buy_price*0.9/script_tick_size)*script_tick_size
            buy_price = int(buy_price/script_tick_size)*script_tick_size
            open_sl_order = next((o for o in all_orders if o['transaction_type'] == 'SELL' and o['status'] in ['TRIGGER PENDING', 'OPEN'] and o['tradingsymbol'] == p['tradingsymbol'] and o['order_type']=='SL'), None)
            open_limit_sell_order = next((o for o in all_orders if o['transaction_type'] == 'SELL' and o['status'] in ['OPEN'] and o['tradingsymbol'] == p['tradingsymbol'] and o['order_type']=='LIMIT'), None)
            # If sl is not placed, place it
            if not open_sl_order and not open_limit_sell_order:
                try:
                    cmp = next(iter(self.broker.get_ltp([p['tradingsymbol']]).values()))['last_price']
                    sl_price = int(cmp * 0.95 / script_tick_size) * script_tick_size
                    print(datetime.now(), "placing_sl_order_for: ", p['tradingsymbol'], 'at price: ', sl_price)
                    open_sl_order_id = self.broker.place_order(tradingsymbol=p['tradingsymbol'], transaction_type='SELL', quantity=p['quantity'],
            product='MIS', order_type='SL', price=(int(sl_price*0.95/script_tick_size)*script_tick_size), trigger_price=sl_price)
                except:
                    print(traceback.format_exc())
                continue
            # cmplist = self.broker.get_quote('NFO:'+p['tradingsymbol'])['NFO:'+p['tradingsymbol']]['depth']['buy']
            # cmplist = [c for c in cmplist if c['price']>=target_price]
            # qty_above_target = sum(c['quantity'] for c in cmplist)
            cmp = next(iter(self.broker.get_ltp([p['tradingsymbol']]).values()))['last_price']
            # Price has already reached target. Cancel sl order and sell asap
            # if cmp>target_price:
            #     try:
            #         print("cancelling_sl_order_for: ", p['tradingsymbol'])
            #         if open_sl_order_id:
            #             self.broker.cancel_order(order_id=open_sl_order_id)
            #             print('placing_target_order_for: ', p['tradingsymbol'], ' at price: ', target_price*0.9)
            #         self.broker.place_order(tradingsymbol=p['tradingsymbol'], transaction_type='SELL', quantity=p['quantity'],
            # product='MIS', order_type='LIMIT', price=buy_price)
            #     except:
            #         print(traceback.format_exc())

            # Keep trailing every 10% increase
            if open_sl_order and open_sl_order['trigger_price'] < int(cmp * 0.95 / script_tick_size) * script_tick_size:
                new_trigger_price = int(0.95 * cmp / script_tick_size) * script_tick_size
                new_price = int(new_trigger_price*0.95/script_tick_size)*script_tick_size
                open_sl_order_id = open_sl_order.get('order_id') if open_sl_order else None
                print(datetime.now(), "setting_trailing_price_for ", p['tradingsymbol'], ", at: ", new_trigger_price)
                try:
                    open_sl_order_id = self.broker.modify_order(order_id=open_sl_order['order_id'], trigger_price=new_trigger_price, price=new_price)
                except:
                    print("could not set trailing sl, cancelling the order for: ", p['tradingsymbol'])
                    print(traceback.format_exc())
                    self.broker.cancel_order(order_id=open_sl_order_id)
                # If there is a corresponding buy order(which means this is an intraday order, try selling in both cases if we have received 10% profit or we have spent 1 hr)
                # if buy_order and (cmp >= 1.1 * buy_price or (datetime.now() - buy_order['order_timestamp']).total_seconds>3600):
                    # Send msg instead of actually buying
                    # loop = asyncio.get_event_loop()
                    # msg = "Sell " + p['tradingsymbol'] + "@ " + str(cmp)
                    # loop.run_until_complete(self.telegram_bot.send_message(chatid=self.telegram_chat_id, msg=msg))
                #     try:
                #         self.broker.place_order(tradingsymbol=p['tradingsymbol'], transaction_type='SELL', quantity=p['quantity'],
                # product='NRML', order_type='LIMIT', price=cmp)
                #     except:
                #         print(traceback.format_exc())

    def start(self):
        while True:
            # next_execution = cron.get_next(datetime)
            # print(next_execution)
            time.sleep(13)
            self.task()


# schedule = "* * * * *"
# cron = croniter(schedule, datetime.now())

z = ZerodhaPositionSqoff()
print(__file__)
z.start()

