import upstox_client
from upstox_client.rest import ApiException

configuration = upstox_client.Configuration(sandbox=True)
configuration.access_token = 'eyJ0eXAiOiJKV1QiLCJrZXlfaWQiOiJza192MS4wIiwiYWxnIjoiSFMyNTYifQ.eyJzdWIiOiIxMzY3NTgiLCJqdGkiOiI2ODBmZDg0NGQ5YzdkYTUwZmQ3NWM0Y2EiLCJpc011bHRpQ2xpZW50IjpmYWxzZSwiaWF0IjoxNzQ1ODY4ODY4LCJpc3MiOiJ1ZGFwaS1nYXRld2F5LXNlcnZpY2UiLCJleHAiOjE3NDg0Njk2MDB9.iHdB2ZkzNvJ-jWcKuyrVEtU-m8MHWWmQbn3fWKyCcZc'

# create an instance of the API class
api_instance = upstox_client.MarketQuoteApi(upstox_client.ApiClient(configuration))
symbol = 'symbol_example' # str | Comma separated list of symbols
interval = 'interval_example' # str | Interval to get ohlc data
api_version = 'api_version_example' # str | API Version Header

try:
    # Market quotes and instruments - OHLC quotes
    api_response = api_instance.get_market_quote_ohlc(symbol, interval, api_version)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling MarketQuoteApi->get_market_quote_ohlc: %s\n" % e)