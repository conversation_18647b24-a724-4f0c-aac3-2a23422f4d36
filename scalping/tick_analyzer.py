import asyncio
import psycopg2
import psycopg2.pool
import pandas as pd
import utils
from telegram_utils import TelegramUtil
from datetime import datetime, timedelta
import logging
logging.basicConfig(level=logging.INFO,
                    format="%(asctime)s %(levelname)s: %(message)s",
                    datefmt="%Y-%m-%d %H:%M:%S")

async def sleep_till_next_interval(interval: int, buffer_seconds: int):
    now = datetime.now()
    # calculate the time of the next interval
    next_interval = now + timedelta(
        minutes=interval - now.minute % interval,
        seconds=-now.second,
        microseconds=-now.microsecond
    )
    sleep_time = (next_interval - now).total_seconds() + buffer_seconds
    # sleep asynchronously, so that it does not stop other tasks
    sleep_task = asyncio.create_task(asyncio.sleep(sleep_time))
    await sleep_task


class TickAnalyzer:
    def __init__(self) -> None:
        config = utils.get_config()
        self.db_conn_pool = psycopg2.pool.ThreadedConnectionPool(
            minconn=2,
            maxconn=4,
            host=config.db_host,
            port=config.db_port,
            database=config.db_name,
            user=config.db_user,
        )
        self.telegram_bot = TelegramUtil(config=config, client=False, bot=True).bot
        self.telegram_chat_id = -1001862665110
        self.df_1min = pd.DataFrame(columns=["time_1min", "symbol", "open", "high", "low", "close", "volume"])
        self.df_5min = pd.DataFrame(columns=["time_5min", "symbol", "open", "high", "low", "close", "volume"])
    
    async def check_price_and_volume(self, df, price_multiplier, vol_multiplier, tf):
        # group the rows in the DataFrame by symbol
        grouped = df.groupby("symbol")
        # iterate over each group
        for symbol, group in grouped:
            # check if there are at least two rows in the group
            # (one for the previous price and volume, and one for the latest price and volume)
            if len(group) < 3:
                continue

            # get the previous and latest prices and volumes for the symbol
            prev_price = group.iloc[-3]["close"]
            latest_price = group.iloc[-2]["close"]
            prev_volume = group.iloc[-3]["volume"]
            latest_volume = group.iloc[-2]["volume"]
            
            if "time_1min" in group.columns:
                latest_time = group.iloc[-2]["time_1min"]
                prev_time = group.iloc[-3]["time_1min"]
            else:
                latest_time = group.iloc[-2]["time_5min"]
                prev_time = group.iloc[-3]["time_5min"]
            latest_time_str = latest_time.strftime("%H:%M")
            prev_time_str = prev_time.strftime("%H:%M")
            # check if the latest price is more than 1.1 times the previous price
            # and the latest volume is more than 10 times the previous volume
            if latest_price > price_multiplier * prev_price and latest_volume > vol_multiplier * prev_volume:
                msg = f"Buy trigerred for {symbol} in {tf} min timeframe between {prev_time_str} and {latest_time_str}, vol: {prev_volume}->{latest_volume}, price: {prev_price}->{latest_price}"
                if latest_time.replace(tzinfo=datetime.now().tzinfo)<datetime.now()-timedelta(minutes=tf+1):
                    msg = "OLD TRIGGER: "+msg
                self.telegram_bot.send_message(self.telegram_chat_id, text=msg)

    async def refresh_5min_df(self):
        # create a function that queries the latest ohlc data from the database
        # and returns it as a pandas DataFrame
        logging.info("refresh_5min_df trigerred")
        def get_latest_data(last_time):
            # create a cursor
            conn = self.db_conn_pool.getconn()
            cur = conn.cursor()
            if last_time:
                # query the latest ohlc data after the specified time
                cur.execute(
                    "SELECT time_5min, symbol, open, high, low, close, volume FROM ohlc_5min WHERE time_5min > %s ORDER BY time_5min",
                    (last_time,)
                )
            else:
                cur.execute(
                    "SELECT time_5min, symbol, open, high, low, close, volume FROM ohlc_5min ORDER BY time_5min"
                )
            # store the query result in a pandas DataFrame
            df = pd.DataFrame(cur.fetchall(), columns=["time_5min", "symbol", "open", "high", "low", "close", "volume"])
            # close the cursor
            cur.close()
            self.db_conn_pool.putconn(conn)
            return df

        last_time = self.df_5min["time_5min"].iloc[-1] if len(self.df_5min) > 0 else None
        while True:
            # get the latest data from the database
            latest_data = get_latest_data(last_time)

            # alert if the latest price is more than 1.1 times the previous price
            # and the latest volume is more than 10 times the previous volume

            # update the DataFrame with the latest data
            # self.df_5min = self.df_5min.append(latest_data, ignore_index=True)
            self.df_5min = pd.concat([self.df_5min, latest_data], ignore_index=True)
            await self.check_price_and_volume(self.df_5min, price_multiplier=1.1, vol_multiplier=10, tf=5)
            # update the last time
            last_time = self.df_5min["time_5min"].iloc[-1] if len(self.df_5min) > 0 else None
    
            logging.info("going_to_sleep_5min")
            await sleep_till_next_interval(interval=5, buffer_seconds=15)

    async def refresh_1min_df(self):
        # create a function that queries the latest ohlc data from the database
        # and returns it as a pandas DataFrame
        logging.info("refresh_1min_df trigerred")
        def get_latest_data(last_time):
            # create a cursor
            conn = self.db_conn_pool.getconn()
            cur = conn.cursor()
            if last_time:
                # query the latest ohlc data after the specified time
                cur.execute(
                    "SELECT time_1min, symbol, open, high, low, close, volume FROM ohlc_1min WHERE time_1min > %s ORDER BY time_1min",
                    (last_time,)
                )
            else:
                cur.execute(
                    "SELECT time_1min, symbol, open, high, low, close, volume FROM ohlc_1min ORDER BY time_1min"
                )
            # store the query result in a pandas DataFrame
            df = pd.DataFrame(cur.fetchall(), columns=["time_1min", "symbol", "open", "high", "low", "close", "volume"])
            # close the cursor
            cur.close()
            self.db_conn_pool.putconn(conn)
            return df

        last_time = self.df_1min["time_1min"].iloc[-1] if len(self.df_1min) > 0 else None
        while True:
            # get the latest data from the database
            latest_data = get_latest_data(last_time)

            # update the DataFrame with the latest data
            # self.df_1min = self.df_1min.append(latest_data, ignore_index=True)
            self.df_1min = pd.concat([self.df_1min, latest_data], ignore_index=True)

            # alert if the latest price is more than 1.1 times the previous price
            # and the latest volume is more than 10 times the previous volume
            await self.check_price_and_volume(latest_data, price_multiplier=1.1, vol_multiplier=2, tf=1)
            # update the last time
            last_time = self.df_1min["time_1min"].iloc[-1] if len(self.df_1min) > 0 else None
            logging.info("going_to_sleep_1min", flush=True)
            await sleep_till_next_interval(interval=1, buffer_seconds=15)


async def main():
    t = TickAnalyzer()
    tasks = []
    tasks.append(asyncio.create_task(t.refresh_1min_df()))
    tasks.append(asyncio.create_task(t.refresh_5min_df()))
    await asyncio.wait(tasks)

asyncio.run(main())
